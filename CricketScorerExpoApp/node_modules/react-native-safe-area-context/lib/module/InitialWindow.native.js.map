{"version": 3, "names": ["NativeSafeAreaContext", "initialWindowMetrics", "getConstants", "initialWindowSafeAreaInsets", "insets"], "sourceRoot": "../../src", "sources": ["InitialWindow.native.ts"], "mappings": "AACA,OAAOA,qBAAqB,MAAM,+BAA+B;AAEjE,OAAO,MAAMC,oBAAoB,GAAID,qBAAqB,EAAEE,YAAY,GAAG,CAAC,EACxED,oBAAoB,IAAI,IAAuB;;AAEnD;AACA;AACA;AACA,OAAO,MAAME,2BAA2B,GAAGF,oBAAoB,EAAEG,MAAM", "ignoreList": []}