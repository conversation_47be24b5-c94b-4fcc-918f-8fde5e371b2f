{"name": "decode-uri-component", "version": "0.2.2", "description": "A better decodeURIComponent", "license": "MIT", "repository": "SamVerschueren/decode-uri-component", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/SamVerschueren"}, "engines": {"node": ">=0.10"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js"], "keywords": ["decode", "uri", "component", "decodeuricomponent", "components", "decoder", "url"], "devDependencies": {"ava": "^0.17.0", "coveralls": "^2.13.1", "nyc": "^10.3.2", "xo": "^0.16.0"}}