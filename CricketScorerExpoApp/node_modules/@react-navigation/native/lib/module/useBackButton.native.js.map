{"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON>", "useBackButton", "ref", "useEffect", "subscription", "addEventListener", "navigation", "current", "canGoBack", "goBack", "remove"], "sourceRoot": "../../src", "sources": ["useBackButton.native.tsx"], "mappings": ";;AAIA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,cAAc;AAE1C,OAAO,SAASC,aAAaA,CAC3BC,GAAkE,EAClE;EACAH,KAAK,CAACI,SAAS,CAAC,MAAM;IACpB,MAAMC,YAAY,GAAGJ,WAAW,CAACK,gBAAgB,CAC/C,mBAAmB,EACnB,MAAM;MACJ,MAAMC,UAAU,GAAGJ,GAAG,CAACK,OAAO;MAE9B,IAAID,UAAU,IAAI,IAAI,EAAE;QACtB,OAAO,KAAK;MACd;MAEA,IAAIA,UAAU,CAACE,SAAS,CAAC,CAAC,EAAE;QAC1BF,UAAU,CAACG,MAAM,CAAC,CAAC;QAEnB,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd,CACF,CAAC;IAED,OAAO,MAAML,YAAY,CAACM,MAAM,CAAC,CAAC;EACpC,CAAC,EAAE,CAACR,GAAG,CAAC,CAAC;AACX", "ignoreList": []}