{"version": 3, "names": ["Platform", "WEB_FONT_STACK", "fonts", "select", "web", "regular", "fontFamily", "fontWeight", "medium", "bold", "heavy", "ios", "default"], "sourceRoot": "../../../src", "sources": ["theming/fonts.tsx"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,cAAc;AAIvC,MAAMC,cAAc,GAClB,uHAAuH;AAEzH,OAAO,MAAMC,KAAK,GAAGF,QAAQ,CAACG,MAAM,CAAC;EACnCC,GAAG,EAAE;IACHC,OAAO,EAAE;MACPC,UAAU,EAAEL,cAAc;MAC1BM,UAAU,EAAE;IACd,CAAC;IACDC,MAAM,EAAE;MACNF,UAAU,EAAEL,cAAc;MAC1BM,UAAU,EAAE;IACd,CAAC;IACDE,IAAI,EAAE;MACJH,UAAU,EAAEL,cAAc;MAC1BM,UAAU,EAAE;IACd,CAAC;IACDG,KAAK,EAAE;MACLJ,UAAU,EAAEL,cAAc;MAC1BM,UAAU,EAAE;IACd;EACF,CAAC;EACDI,GAAG,EAAE;IACHN,OAAO,EAAE;MACPC,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE;IACd,CAAC;IACDC,MAAM,EAAE;MACNF,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE;IACd,CAAC;IACDE,IAAI,EAAE;MACJH,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE;IACd,CAAC;IACDG,KAAK,EAAE;MACLJ,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE;IACd;EACF,CAAC;EACDK,OAAO,EAAE;IACPP,OAAO,EAAE;MACPC,UAAU,EAAE,YAAY;MACxBC,UAAU,EAAE;IACd,CAAC;IACDC,MAAM,EAAE;MACNF,UAAU,EAAE,mBAAmB;MAC/BC,UAAU,EAAE;IACd,CAAC;IACDE,IAAI,EAAE;MACJH,UAAU,EAAE,YAAY;MACxBC,UAAU,EAAE;IACd,CAAC;IACDG,KAAK,EAAE;MACLJ,UAAU,EAAE,YAAY;MACxBC,UAAU,EAAE;IACd;EACF;AACF,CAAmD,CAAC", "ignoreList": []}