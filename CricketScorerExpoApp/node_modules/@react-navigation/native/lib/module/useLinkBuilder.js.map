{"version": 3, "names": ["CommonActions", "findFocusedRoute", "getActionFromState", "getPathFromState", "getStateFromPath", "NavigationHelpersContext", "NavigationRouteContext", "useStateForPath", "React", "LinkingContext", "useLinkBuilder", "navigation", "useContext", "route", "options", "focusedRouteState", "getPathFromStateHelper", "getStateFromPathHelper", "getActionFromStateHelper", "buildHref", "useCallback", "name", "params", "enabled", "undefined", "isScreen", "key", "getState", "routes", "some", "r", "stateForRoute", "constructState", "state", "path", "config", "buildAction", "href", "startsWith", "Error", "action", "reset"], "sourceRoot": "../../src", "sources": ["useLinkBuilder.tsx"], "mappings": ";;AAAA,SACEA,aAAa,EACbC,gBAAgB,EAChBC,kBAAkB,EAClBC,gBAAgB,EAChBC,gBAAgB,EAChBC,wBAAwB,EACxBC,sBAAsB,EACtBC,eAAe,QACV,wBAAwB;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,SAASC,cAAc,QAAQ,qBAAkB;AAMjD;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAAA,EAAG;EAC/B,MAAMC,UAAU,GAAGH,KAAK,CAACI,UAAU,CAACP,wBAAwB,CAAC;EAC7D,MAAMQ,KAAK,GAAGL,KAAK,CAACI,UAAU,CAACN,sBAAsB,CAAC;EAEtD,MAAM;IAAEQ;EAAQ,CAAC,GAAGN,KAAK,CAACI,UAAU,CAACH,cAAc,CAAC;EAEpD,MAAMM,iBAAiB,GAAGR,eAAe,CAAC,CAAC;EAE3C,MAAMS,sBAAsB,GAAGF,OAAO,EAAEX,gBAAgB,IAAIA,gBAAgB;EAC5E,MAAMc,sBAAsB,GAAGH,OAAO,EAAEV,gBAAgB,IAAIA,gBAAgB;EAC5E,MAAMc,wBAAwB,GAC5BJ,OAAO,EAAEZ,kBAAkB,IAAIA,kBAAkB;EAEnD,MAAMiB,SAAS,GAAGX,KAAK,CAACY,WAAW,CACjC,CAACC,IAAY,EAAEC,MAAe,KAAK;IACjC,IAAIR,OAAO,EAAES,OAAO,KAAK,KAAK,EAAE;MAC9B,OAAOC,SAAS;IAClB;;IAEA;IACA;IACA;IACA;IACA;IACA,MAAMC,QAAQ,GACZd,UAAU,IAAIE,KAAK,EAAEa,GAAG,IAAIX,iBAAiB,GACzCF,KAAK,CAACa,GAAG,KAAKzB,gBAAgB,CAACc,iBAAiB,CAAC,EAAEW,GAAG,IACtDf,UAAU,CAACgB,QAAQ,CAAC,CAAC,CAACC,MAAM,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACJ,GAAG,KAAKb,KAAK,CAACa,GAAG,CAAC,GAC7D,KAAK;IAEX,MAAMK,aAA2B,GAAG;MAClCH,MAAM,EAAE,CAAC;QAAEP,IAAI;QAAEC;MAAO,CAAC;IAC3B,CAAC;IAED,MAAMU,cAAc,GAClBC,KAA+B,IACd;MACjB,IAAIA,KAAK,EAAE;QACT,MAAMpB,KAAK,GAAGoB,KAAK,CAACL,MAAM,CAAC,CAAC,CAAC;;QAE7B;QACA;QACA;QACA,IAAIH,QAAQ,IAAI,CAACZ,KAAK,CAACoB,KAAK,EAAE;UAC5B,OAAOF,aAAa;QACtB;;QAEA;QACA,OAAO;UACLH,MAAM,EAAE,CACN;YACE,GAAGf,KAAK;YACRoB,KAAK,EAAED,cAAc,CAACnB,KAAK,CAACoB,KAAK;UACnC,CAAC;QAEL,CAAC;MACH;;MAEA;MACA;MACA;MACA;MACA,OAAOF,aAAa;IACtB,CAAC;IAED,MAAME,KAAK,GAAGD,cAAc,CAACjB,iBAAiB,CAAC;IAC/C,MAAMmB,IAAI,GAAGlB,sBAAsB,CAACiB,KAAK,EAAEnB,OAAO,EAAEqB,MAAM,CAAC;IAE3D,OAAOD,IAAI;EACb,CAAC,EACD,CACEpB,OAAO,EAAES,OAAO,EAChBT,OAAO,EAAEqB,MAAM,EACftB,KAAK,EAAEa,GAAG,EACVf,UAAU,EACVI,iBAAiB,EACjBC,sBAAsB,CAE1B,CAAC;EAED,MAAMoB,WAAW,GAAG5B,KAAK,CAACY,WAAW,CAClCiB,IAAY,IAAK;IAChB,IAAI,CAACA,IAAI,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;MACzB,MAAM,IAAIC,KAAK,CAAC,iCAAiCF,IAAI,IAAI,CAAC;IAC5D;IAEA,MAAMJ,KAAK,GAAGhB,sBAAsB,CAACoB,IAAI,EAAEvB,OAAO,EAAEqB,MAAM,CAAC;IAE3D,IAAIF,KAAK,EAAE;MACT,MAAMO,MAAM,GAAGtB,wBAAwB,CAACe,KAAK,EAAEnB,OAAO,EAAEqB,MAAM,CAAC;MAE/D,OAAOK,MAAM,IAAIxC,aAAa,CAACyC,KAAK,CAACR,KAAK,CAAC;IAC7C,CAAC,MAAM;MACL,MAAM,IAAIM,KAAK,CAAC,iDAAiD,CAAC;IACpE;EACF,CAAC,EACD,CAACzB,OAAO,EAAEqB,MAAM,EAAElB,sBAAsB,EAAEC,wBAAwB,CACpE,CAAC;EAED,OAAO;IACLC,SAAS;IACTiB;EACF,CAAC;AACH", "ignoreList": []}