{"version": 3, "names": ["React", "LocaleDirContext", "useLocale", "direction", "useContext", "undefined", "Error"], "sourceRoot": "../../src", "sources": ["useLocale.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,gBAAgB,QAAQ,uBAAoB;;AAErD;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAAA,EAAG;EAC1B,MAAMC,SAAS,GAAGH,KAAK,CAACI,UAAU,CAACH,gBAAgB,CAAC;EAEpD,IAAIE,SAAS,KAAKE,SAAS,EAAE;IAC3B,MAAM,IAAIC,KAAK,CACb,sFACF,CAAC;EACH;EAEA,OAAO;IAAEH;EAAU,CAAC;AACtB", "ignoreList": []}