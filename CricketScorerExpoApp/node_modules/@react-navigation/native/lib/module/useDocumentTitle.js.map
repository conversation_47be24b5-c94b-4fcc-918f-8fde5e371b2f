{"version": 3, "names": ["React", "useDocumentTitle", "ref", "enabled", "formatter", "options", "route", "title", "name", "useEffect", "navigation", "current", "getCurrentOptions", "getCurrentRoute", "document", "addListener", "e", "data"], "sourceRoot": "../../src", "sources": ["useDocumentTitle.tsx"], "mappings": ";;AAIA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAI9B;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAC9BC,GAAkE,EAClE;EACEC,OAAO,GAAG,IAAI;EACdC,SAAS,GAAGA,CAACC,OAAO,EAAEC,KAAK,KAAKD,OAAO,EAAEE,KAAK,IAAID,KAAK,EAAEE;AACrC,CAAC,GAAG,CAAC,CAAC,EAC5B;EACAR,KAAK,CAACS,SAAS,CAAC,MAAM;IACpB,IAAI,CAACN,OAAO,EAAE;MACZ;IACF;IAEA,MAAMO,UAAU,GAAGR,GAAG,CAACS,OAAO;IAE9B,IAAID,UAAU,EAAE;MACd,MAAMH,KAAK,GAAGH,SAAS,CACrBM,UAAU,CAACE,iBAAiB,CAAC,CAAC,EAC9BF,UAAU,CAACG,eAAe,CAAC,CAC7B,CAAC;MAEDC,QAAQ,CAACP,KAAK,GAAGA,KAAK;IACxB;IAEA,OAAOG,UAAU,EAAEK,WAAW,CAAC,SAAS,EAAGC,CAAC,IAAK;MAC/C,MAAMT,KAAK,GAAGH,SAAS,CAACY,CAAC,CAACC,IAAI,CAACZ,OAAO,EAAEK,UAAU,EAAEG,eAAe,CAAC,CAAC,CAAC;MAEtEC,QAAQ,CAACP,KAAK,GAAGA,KAAK;IACxB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}