{"version": 3, "names": ["useTheme", "React", "Animated", "jsx", "_jsx", "Background", "style", "rest", "colors", "View", "flex", "backgroundColor", "background"], "sourceRoot": "../../src", "sources": ["Background.tsx"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,QAIH,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAOtB,OAAO,SAASC,UAAUA,CAAC;EAAEC,KAAK;EAAE,GAAGC;AAAY,CAAC,EAAE;EACpD,MAAM;IAAEC;EAAO,CAAC,GAAGR,QAAQ,CAAC,CAAC;EAE7B,oBACEI,IAAA,CAACF,QAAQ,CAACO,IAAI;IAAA,GACRF,IAAI;IACRD,KAAK,EAAE,CAAC;MAAEI,IAAI,EAAE,CAAC;MAAEC,eAAe,EAAEH,MAAM,CAACI;IAAW,CAAC,EAAEN,KAAK;EAAE,CACjE,CAAC;AAEN", "ignoreList": []}