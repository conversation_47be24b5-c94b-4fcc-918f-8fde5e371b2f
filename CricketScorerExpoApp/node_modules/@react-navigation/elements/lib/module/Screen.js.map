{"version": 3, "names": ["NavigationContext", "NavigationRouteContext", "React", "StyleSheet", "View", "useSafeAreaFrame", "useSafeAreaInsets", "Background", "getDefaultHeaderHeight", "HeaderHeightContext", "HeaderShownContext", "jsx", "_jsx", "jsxs", "_jsxs", "Screen", "props", "dimensions", "insets", "isParentHeaderShown", "useContext", "parentHeaderHeight", "focused", "modal", "header", "headerShown", "headerTransparent", "headerStatusBarHeight", "top", "navigation", "route", "children", "style", "headerHeight", "setHeaderHeight", "useState", "styles", "container", "collapsable", "Provider", "value", "pointerEvents", "onLayout", "e", "height", "nativeEvent", "layout", "absolute", "content", "create", "flex", "zIndex", "position", "start", "end"], "sourceRoot": "../../src", "sources": ["Screen.tsx"], "mappings": ";;AAAA,SACEA,iBAAiB,EAEjBC,sBAAsB,QAGjB,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAGEC,UAAU,EACVC,IAAI,QAEC,cAAc;AACrB,SACEC,gBAAgB,EAChBC,iBAAiB,QACZ,gCAAgC;AAEvC,SAASC,UAAU,QAAQ,iBAAc;AACzC,SAASC,sBAAsB,QAAQ,oCAAiC;AACxE,SAASC,mBAAmB,QAAQ,iCAA8B;AAClE,SAASC,kBAAkB,QAAQ,gCAA6B;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAejE,OAAO,SAASC,MAAMA,CAACC,KAAY,EAAE;EACnC,MAAMC,UAAU,GAAGZ,gBAAgB,CAAC,CAAC;EACrC,MAAMa,MAAM,GAAGZ,iBAAiB,CAAC,CAAC;EAElC,MAAMa,mBAAmB,GAAGjB,KAAK,CAACkB,UAAU,CAACV,kBAAkB,CAAC;EAChE,MAAMW,kBAAkB,GAAGnB,KAAK,CAACkB,UAAU,CAACX,mBAAmB,CAAC;EAEhE,MAAM;IACJa,OAAO;IACPC,KAAK,GAAG,KAAK;IACbC,MAAM;IACNC,WAAW,GAAG,IAAI;IAClBC,iBAAiB;IACjBC,qBAAqB,GAAGR,mBAAmB,GAAG,CAAC,GAAGD,MAAM,CAACU,GAAG;IAC5DC,UAAU;IACVC,KAAK;IACLC,QAAQ;IACRC;EACF,CAAC,GAAGhB,KAAK;EAET,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGhC,KAAK,CAACiC,QAAQ,CAAC,MACrD3B,sBAAsB,CAACS,UAAU,EAAEM,KAAK,EAAEI,qBAAqB,CACjE,CAAC;EAED,oBACEb,KAAA,CAACP,UAAU;IACT,eAAa,CAACe,OAAQ;IACtBU,KAAK,EAAE,CAACI,MAAM,CAACC,SAAS,EAAEL,KAAK;IAC/B;IACA;IAAA;IACAM,WAAW,EAAE,KAAM;IAAAP,QAAA,GAElBN,WAAW,gBACVb,IAAA,CAACZ,iBAAiB,CAACuC,QAAQ;MAACC,KAAK,EAAEX,UAAW;MAAAE,QAAA,eAC5CnB,IAAA,CAACX,sBAAsB,CAACsC,QAAQ;QAACC,KAAK,EAAEV,KAAM;QAAAC,QAAA,eAC5CnB,IAAA,CAACR,IAAI;UACHqC,aAAa,EAAC,UAAU;UACxBC,QAAQ,EAAGC,CAAC,IAAK;YACf,MAAM;cAAEC;YAAO,CAAC,GAAGD,CAAC,CAACE,WAAW,CAACC,MAAM;YAEvCZ,eAAe,CAACU,MAAM,CAAC;UACzB,CAAE;UACFZ,KAAK,EAAE,CACLI,MAAM,CAACZ,MAAM,EACbE,iBAAiB,GAAGU,MAAM,CAACW,QAAQ,GAAG,IAAI,CAC1C;UAAAhB,QAAA,EAEDP;QAAM,CACH;MAAC,CACwB;IAAC,CACR,CAAC,GAC3B,IAAI,eACRZ,IAAA,CAACR,IAAI;MAAC4B,KAAK,EAAEI,MAAM,CAACY,OAAQ;MAAAjB,QAAA,eAC1BnB,IAAA,CAACF,kBAAkB,CAAC6B,QAAQ;QAC1BC,KAAK,EAAErB,mBAAmB,IAAIM,WAAW,KAAK,KAAM;QAAAM,QAAA,eAEpDnB,IAAA,CAACH,mBAAmB,CAAC8B,QAAQ;UAC3BC,KAAK,EAAEf,WAAW,GAAGQ,YAAY,GAAIZ,kBAAkB,IAAI,CAAG;UAAAU,QAAA,EAE7DA;QAAQ,CACmB;MAAC,CACJ;IAAC,CAC1B,CAAC;EAAA,CACG,CAAC;AAEjB;AAEA,MAAMK,MAAM,GAAGjC,UAAU,CAAC8C,MAAM,CAAC;EAC/BZ,SAAS,EAAE;IACTa,IAAI,EAAE;EACR,CAAC;EACDF,OAAO,EAAE;IACPE,IAAI,EAAE;EACR,CAAC;EACD1B,MAAM,EAAE;IACN2B,MAAM,EAAE;EACV,CAAC;EACDJ,QAAQ,EAAE;IACRK,QAAQ,EAAE,UAAU;IACpBxB,GAAG,EAAE,CAAC;IACNyB,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE;EACP;AACF,CAAC,CAAC", "ignoreList": []}