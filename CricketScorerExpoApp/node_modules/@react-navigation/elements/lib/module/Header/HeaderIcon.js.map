{"version": 3, "names": ["useLocale", "useTheme", "Image", "Platform", "StyleSheet", "jsx", "_jsx", "HeaderIcon", "source", "style", "rest", "colors", "direction", "resizeMode", "fadeDuration", "tintColor", "text", "styles", "icon", "flip", "ICON_SIZE", "OS", "ICON_MARGIN", "create", "width", "height", "margin", "transform"], "sourceRoot": "../../../src", "sources": ["Header/HeaderIcon.tsx"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,0BAA0B;AAC9D,SAASC,KAAK,EAAmBC,QAAQ,EAAEC,UAAU,QAAQ,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAE5E,OAAO,SAASC,UAAUA,CAAC;EAAEC,MAAM;EAAEC,KAAK;EAAE,GAAGC;AAAiB,CAAC,EAAE;EACjE,MAAM;IAAEC;EAAO,CAAC,GAAGV,QAAQ,CAAC,CAAC;EAC7B,MAAM;IAAEW;EAAU,CAAC,GAAGZ,SAAS,CAAC,CAAC;EAEjC,oBACEM,IAAA,CAACJ,KAAK;IACJM,MAAM,EAAEA,MAAO;IACfK,UAAU,EAAC,SAAS;IACpBC,YAAY,EAAE,CAAE;IAChBC,SAAS,EAAEJ,MAAM,CAACK,IAAK;IACvBP,KAAK,EAAE,CAACQ,MAAM,CAACC,IAAI,EAAEN,SAAS,KAAK,KAAK,IAAIK,MAAM,CAACE,IAAI,EAAEV,KAAK,CAAE;IAAA,GAC5DC;EAAI,CACT,CAAC;AAEN;AAEA,OAAO,MAAMU,SAAS,GAAGjB,QAAQ,CAACkB,EAAE,KAAK,KAAK,GAAG,EAAE,GAAG,EAAE;AACxD,OAAO,MAAMC,WAAW,GAAGnB,QAAQ,CAACkB,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC;AAExD,MAAMJ,MAAM,GAAGb,UAAU,CAACmB,MAAM,CAAC;EAC/BL,IAAI,EAAE;IACJM,KAAK,EAAEJ,SAAS;IAChBK,MAAM,EAAEL,SAAS;IACjBM,MAAM,EAAEJ;EACV,CAAC;EACDH,IAAI,EAAE;IACJQ,SAAS,EAAE;EACb;AACF,CAAC,CAAC", "ignoreList": []}