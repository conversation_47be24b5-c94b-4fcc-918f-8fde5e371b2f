{"version": 3, "names": ["useTheme", "React", "Animated", "Platform", "StyleSheet", "jsx", "_jsx", "HeaderBackground", "style", "rest", "colors", "dark", "View", "styles", "container", "backgroundColor", "card", "borderBottomColor", "border", "OS", "shadowColor", "create", "flex", "select", "android", "elevation", "ios", "shadowOpacity", "shadowRadius", "shadowOffset", "width", "height", "hairlineWidth", "default", "borderBottomWidth"], "sourceRoot": "../../../src", "sources": ["Header/HeaderBackground.tsx"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EACRC,QAAQ,EAERC,UAAU,QAGL,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAOtB,OAAO,SAASC,gBAAgBA,CAAC;EAAEC,KAAK;EAAE,GAAGC;AAAY,CAAC,EAAE;EAC1D,MAAM;IAAEC,MAAM;IAAEC;EAAK,CAAC,GAAGX,QAAQ,CAAC,CAAC;EAEnC,oBACEM,IAAA,CAACJ,QAAQ,CAACU,IAAI;IACZJ,KAAK,EAAE,CACLK,MAAM,CAACC,SAAS,EAChB;MACEC,eAAe,EAAEL,MAAM,CAACM,IAAI;MAC5BC,iBAAiB,EAAEP,MAAM,CAACQ,MAAM;MAChC,IAAIf,QAAQ,CAACgB,EAAE,KAAK,KAAK,IAAI;QAC3BC,WAAW,EAAET,IAAI,GACb,2BAA2B,GAC3B;MACN,CAAC;IACH,CAAC,EACDH,KAAK,CACL;IAAA,GACEC;EAAI,CACT,CAAC;AAEN;AAEA,MAAMI,MAAM,GAAGT,UAAU,CAACiB,MAAM,CAAC;EAC/BP,SAAS,EAAE;IACTQ,IAAI,EAAE,CAAC;IACP,GAAGnB,QAAQ,CAACoB,MAAM,CAAC;MACjBC,OAAO,EAAE;QACPC,SAAS,EAAE;MACb,CAAC;MACDC,GAAG,EAAE;QACHC,aAAa,EAAE,GAAG;QAClBC,YAAY,EAAE,CAAC;QACfC,YAAY,EAAE;UACZC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE3B,UAAU,CAAC4B;QACrB;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,iBAAiB,EAAE9B,UAAU,CAAC4B;MAChC;IACF,CAAC;EACH;AACF,CAAC,CAAC", "ignoreList": []}