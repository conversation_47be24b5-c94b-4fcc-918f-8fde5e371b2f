{"version": 3, "names": ["PixelRatio", "Platform", "getDefaultHeaderHeight", "layout", "modalPresentation", "topInset", "headerHeight", "hasDynamicIsland", "OS", "statusBarHeight", "get", "isLandscape", "width", "height", "isPad", "isTV"], "sourceRoot": "../../../src", "sources": ["Header/getDefaultHeaderHeight.tsx"], "mappings": ";;AAAA,SAASA,UAAU,EAAEC,QAAQ,QAAQ,cAAc;AAInD,OAAO,SAASC,sBAAsBA,CACpCC,MAAc,EACdC,iBAA0B,EAC1BC,QAAgB,EACR;EACR,IAAIC,YAAY;;EAEhB;EACA,MAAMC,gBAAgB,GAAGN,QAAQ,CAACO,EAAE,KAAK,KAAK,IAAIH,QAAQ,GAAG,EAAE;EAC/D,MAAMI,eAAe,GAAGF,gBAAgB,GACpCF,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAGL,UAAU,CAACU,GAAG,CAAC,CAAC,CAAC,GACrCL,QAAQ;EAEZ,MAAMM,WAAW,GAAGR,MAAM,CAACS,KAAK,GAAGT,MAAM,CAACU,MAAM;EAEhD,IAAIZ,QAAQ,CAACO,EAAE,KAAK,KAAK,EAAE;IACzB,IAAIP,QAAQ,CAACa,KAAK,IAAIb,QAAQ,CAACc,IAAI,EAAE;MACnC,IAAIX,iBAAiB,EAAE;QACrBE,YAAY,GAAG,EAAE;MACnB,CAAC,MAAM;QACLA,YAAY,GAAG,EAAE;MACnB;IACF,CAAC,MAAM;MACL,IAAIK,WAAW,EAAE;QACfL,YAAY,GAAG,EAAE;MACnB,CAAC,MAAM;QACL,IAAIF,iBAAiB,EAAE;UACrBE,YAAY,GAAG,EAAE;QACnB,CAAC,MAAM;UACLA,YAAY,GAAG,EAAE;QACnB;MACF;IACF;EACF,CAAC,MAAM;IACLA,YAAY,GAAG,EAAE;EACnB;EAEA,OAAOA,YAAY,GAAGG,eAAe;AACvC", "ignoreList": []}