{"version": 3, "names": ["useLocale", "useTheme", "React", "Animated", "Image", "Platform", "StyleSheet", "View", "backIcon", "backIconMask", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HeaderIcon", "ICON_MARGIN", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled", "allowFontScaling", "backImage", "label", "labelStyle", "displayMode", "OS", "onLabelLayout", "onPress", "pressColor", "pressOpacity", "screenLayout", "tintColor", "titleLayout", "truncatedLabel", "accessibilityLabel", "testID", "style", "href", "colors", "fonts", "direction", "labelWidth", "<PERSON><PERSON><PERSON><PERSON>", "useState", "truncated<PERSON><PERSON><PERSON><PERSON>", "setT<PERSON><PERSON><PERSON><PERSON><PERSON>", "renderBackImage", "text", "source", "styles", "icon", "iconWithLabel", "renderLabel", "availableSpace", "width", "ICON_WIDTH", "potentialLabelText", "finalLabelText", "commonStyle", "regular", "hiddenStyle", "position", "top", "left", "opacity", "labelElement", "labelWrapper", "children", "Text", "numberOfLines", "onLayout", "e", "nativeEvent", "layout", "accessible", "color", "maskElement", "iconMaskContainer", "min<PERSON><PERSON><PERSON>", "resizeMode", "iconMask", "flip", "iconMaskFillerRect", "handlePress", "requestAnimationFrame", "container", "Fragment", "ICON_MARGIN_END", "create", "paddingHorizontal", "hairlineWidth", "select", "ios", "default", "marginVertical", "marginHorizontal", "fontSize", "letterSpacing", "flexDirection", "alignItems", "marginEnd", "flex", "justifyContent", "backgroundColor", "height", "marginStart", "alignSelf", "transform"], "sourceRoot": "../../../src", "sources": ["Header/HeaderBackButton.tsx"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,0BAA0B;AAC9D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EACRC,KAAK,EACLC,QAAQ,EAERC,UAAU,EAEVC,IAAI,QACC,cAAc;AAErB,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,SAASC,UAAU,QAAQ,eAAe;AAE1C,SAASC,YAAY,QAAQ,mBAAgB;AAC7C,SAASC,UAAU,EAAEC,WAAW,QAAQ,iBAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEvD,OAAO,SAASC,gBAAgBA,CAAC;EAC/BC,QAAQ;EACRC,gBAAgB;EAChBC,SAAS;EACTC,KAAK;EACLC,UAAU;EACVC,WAAW,GAAGnB,QAAQ,CAACoB,EAAE,KAAK,KAAK,GAAG,SAAS,GAAG,SAAS;EAC3DC,aAAa;EACbC,OAAO;EACPC,UAAU;EACVC,YAAY;EACZC,YAAY;EACZC,SAAS;EACTC,WAAW;EACXC,cAAc,GAAG,MAAM;EACvBC,kBAAkB,GAAGZ,KAAK,IAAIA,KAAK,KAAK,MAAM,GAAG,GAAGA,KAAK,QAAQ,GAAG,SAAS;EAC7Ea,MAAM;EACNC,KAAK;EACLC;AACqB,CAAC,EAAE;EACxB,MAAM;IAAEC,MAAM;IAAEC;EAAM,CAAC,GAAGtC,QAAQ,CAAC,CAAC;EACpC,MAAM;IAAEuC;EAAU,CAAC,GAAGxC,SAAS,CAAC,CAAC;EAEjC,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,KAAK,CAACyC,QAAQ,CAAgB,IAAI,CAAC;EACvE,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3C,KAAK,CAACyC,QAAQ,CAElE,IAAI,CAAC;EAEP,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIzB,SAAS,EAAE;MACb,OAAOA,SAAS,CAAC;QAAEU,SAAS,EAAEA,SAAS,IAAIO,MAAM,CAACS;MAAK,CAAC,CAAC;IAC3D,CAAC,MAAM;MACL,oBACEhC,IAAA,CAACH,UAAU;QACToC,MAAM,EAAExC,QAAS;QACjBuB,SAAS,EAAEA,SAAU;QACrBK,KAAK,EAAE,CACLa,MAAM,CAACC,IAAI,EACX1B,WAAW,KAAK,SAAS,IAAIyB,MAAM,CAACE,aAAa;MACjD,CACH,CAAC;IAEN;EACF,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI5B,WAAW,KAAK,SAAS,EAAE;MAC7B,OAAO,IAAI;IACb;IAEA,MAAM6B,cAAc,GAClBrB,WAAW,IAAIF,YAAY,GACvB,CAACA,YAAY,CAACwB,KAAK,GAAGtB,WAAW,CAACsB,KAAK,IAAI,CAAC,IAC3CC,UAAU,GAAG1C,WAAW,CAAC,GAC1B,IAAI;IAEV,MAAM2C,kBAAkB,GACtBhC,WAAW,KAAK,SAAS,GAAGF,KAAK,GAAGW,cAAc;IACpD,MAAMwB,cAAc,GAClBJ,cAAc,IAAIZ,UAAU,IAAIG,mBAAmB,GAC/CS,cAAc,GAAGZ,UAAU,GACzBe,kBAAkB,GAClBH,cAAc,GAAGT,mBAAmB,GAClCX,cAAc,GACd,IAAI,GACRuB,kBAAkB;IAExB,MAAME,WAA6D,GAAG,CACpEnB,KAAK,CAACoB,OAAO,EACbV,MAAM,CAAC3B,KAAK,EACZC,UAAU,CACX;IAED,MAAMqC,WAA6D,GAAG,CACpEF,WAAW,EACX;MACEG,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,OAAO,EAAE;IACX,CAAC,CACF;IAED,MAAMC,YAAY,gBAChBhD,KAAA,CAACV,IAAI;MAAC6B,KAAK,EAAEa,MAAM,CAACiB,YAAa;MAAAC,QAAA,GAC9B7C,KAAK,IAAIE,WAAW,KAAK,SAAS,gBACjCT,IAAA,CAACZ,QAAQ,CAACiE,IAAI;QACZhC,KAAK,EAAEwB,WAAY;QACnBS,aAAa,EAAE,CAAE;QACjBC,QAAQ,EAAGC,CAAC,IAAK7B,aAAa,CAAC6B,CAAC,CAACC,WAAW,CAACC,MAAM,CAACnB,KAAK,CAAE;QAAAa,QAAA,EAE1D7C;MAAK,CACO,CAAC,GACd,IAAI,EACPW,cAAc,gBACblB,IAAA,CAACZ,QAAQ,CAACiE,IAAI;QACZhC,KAAK,EAAEwB,WAAY;QACnBS,aAAa,EAAE,CAAE;QACjBC,QAAQ,EAAGC,CAAC,IAAK1B,sBAAsB,CAAC0B,CAAC,CAACC,WAAW,CAACC,MAAM,CAACnB,KAAK,CAAE;QAAAa,QAAA,EAEnElC;MAAc,CACF,CAAC,GACd,IAAI,EACPwB,cAAc,gBACb1C,IAAA,CAACZ,QAAQ,CAACiE,IAAI;QACZM,UAAU,EAAE,KAAM;QAClBJ,QAAQ,EAAE5C,aAAc;QACxBU,KAAK,EAAE,CAACL,SAAS,GAAG;UAAE4C,KAAK,EAAE5C;QAAU,CAAC,GAAG,IAAI,EAAE2B,WAAW,CAAE;QAC9DW,aAAa,EAAE,CAAE;QACjBjD,gBAAgB,EAAE,CAAC,CAACA,gBAAiB;QAAA+C,QAAA,EAEpCV;MAAc,CACF,CAAC,GACd,IAAI;IAAA,CACJ,CACP;IAED,IAAIpC,SAAS,IAAIhB,QAAQ,CAACoB,EAAE,KAAK,KAAK,EAAE;MACtC;MACA;MACA,OAAOwC,YAAY;IACrB;IAEA,oBACElD,IAAA,CAACL,UAAU;MACTkE,WAAW,eACT3D,KAAA,CAACV,IAAI;QACH6B,KAAK,EAAE,CACLa,MAAM,CAAC4B,iBAAiB;QACxB;QACA/C,YAAY,GAAG;UAAEgD,QAAQ,EAAEhD,YAAY,CAACwB,KAAK,GAAG,CAAC,GAAG;QAAG,CAAC,GAAG,IAAI,CAC/D;QAAAa,QAAA,gBAEFpD,IAAA,CAACX,KAAK;UACJ4C,MAAM,EAAEvC,YAAa;UACrBsE,UAAU,EAAC,SAAS;UACpB3C,KAAK,EAAE,CAACa,MAAM,CAAC+B,QAAQ,EAAExC,SAAS,KAAK,KAAK,IAAIS,MAAM,CAACgC,IAAI;QAAE,CAC9D,CAAC,eACFlE,IAAA,CAACR,IAAI;UAAC6B,KAAK,EAAEa,MAAM,CAACiC;QAAmB,CAAE,CAAC;MAAA,CACtC,CACP;MAAAf,QAAA,EAEAF;IAAY,CACH,CAAC;EAEjB,CAAC;EAED,MAAMkB,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIxD,OAAO,EAAE;MACXyD,qBAAqB,CAAC,MAAMzD,OAAO,CAAC,CAAC,CAAC;IACxC;EACF,CAAC;EAED,oBACEZ,IAAA,CAACJ,YAAY;IACXQ,QAAQ,EAAEA,QAAS;IACnBkB,IAAI,EAAEA,IAAK;IACXH,kBAAkB,EAAEA,kBAAmB;IACvCC,MAAM,EAAEA,MAAO;IACfR,OAAO,EAAEwD,WAAY;IACrBvD,UAAU,EAAEA,UAAW;IACvBC,YAAY,EAAEA,YAAa;IAC3BO,KAAK,EAAE,CAACa,MAAM,CAACoC,SAAS,EAAEjD,KAAK,CAAE;IAAA+B,QAAA,eAEjClD,KAAA,CAACf,KAAK,CAACoF,QAAQ;MAAAnB,QAAA,GACZrB,eAAe,CAAC,CAAC,EACjBM,WAAW,CAAC,CAAC;IAAA,CACA;EAAC,CACL,CAAC;AAEnB;AAEA,MAAMG,UAAU,GAAGlD,QAAQ,CAACoB,EAAE,KAAK,KAAK,GAAG,EAAE,GAAG,EAAE;AAClD,MAAM8D,eAAe,GAAGlF,QAAQ,CAACoB,EAAE,KAAK,KAAK,GAAG,EAAE,GAAG,CAAC;AAEtD,MAAMwB,MAAM,GAAG3C,UAAU,CAACkF,MAAM,CAAC;EAC/BH,SAAS,EAAE;IACTI,iBAAiB,EAAE,CAAC;IACpBX,QAAQ,EAAExE,UAAU,CAACoF,aAAa;IAAE;IACpC,GAAGrF,QAAQ,CAACsF,MAAM,CAAC;MACjBC,GAAG,EAAE,IAAI;MACTC,OAAO,EAAE;QACPC,cAAc,EAAE,CAAC;QACjBC,gBAAgB,EAAE;MACpB;IACF,CAAC;EACH,CAAC;EACDzE,KAAK,EAAE;IACL0E,QAAQ,EAAE,EAAE;IACZ;IACA;IACAC,aAAa,EAAE;EACjB,CAAC;EACD/B,YAAY,EAAE;IACZ;IACA;IACAgC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,YAAY;IACxBC,SAAS,EAAEvF;EACb,CAAC;EACDqC,IAAI,EAAE;IACJI,KAAK,EAAEC,UAAU;IACjB6C,SAAS,EAAEb;EACb,CAAC;EACDpC,aAAa,EACX9C,QAAQ,CAACoB,EAAE,KAAK,KAAK,GACjB;IACE2E,SAAS,EAAE;EACb,CAAC,GACD,CAAC,CAAC;EACRvB,iBAAiB,EAAE;IACjBwB,IAAI,EAAE,CAAC;IACPH,aAAa,EAAE,KAAK;IACpBI,cAAc,EAAE;EAClB,CAAC;EACDpB,kBAAkB,EAAE;IAClBmB,IAAI,EAAE,CAAC;IACPE,eAAe,EAAE;EACnB,CAAC;EACDvB,QAAQ,EAAE;IACRwB,MAAM,EAAE,EAAE;IACVlD,KAAK,EAAE,EAAE;IACTmD,WAAW,EAAE,CAAC,IAAI;IAClBX,cAAc,EAAE,EAAE;IAClBY,SAAS,EAAE;EACb,CAAC;EACDzB,IAAI,EAAE;IACJ0B,SAAS,EAAE;EACb;AACF,CAAC,CAAC", "ignoreList": []}