{"version": 3, "names": ["React", "HeaderHeightContext", "useHeaderHeight", "height", "useContext", "undefined", "Error"], "sourceRoot": "../../../src", "sources": ["Header/useHeaderHeight.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,mBAAmB,QAAQ,0BAAuB;AAE3D,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,MAAMC,MAAM,GAAGH,KAAK,CAACI,UAAU,CAACH,mBAAmB,CAAC;EAEpD,IAAIE,MAAM,KAAKE,SAAS,EAAE;IACxB,MAAM,IAAIC,KAAK,CACb,wFACF,CAAC;EACH;EAEA,OAAOH,MAAM;AACf", "ignoreList": []}