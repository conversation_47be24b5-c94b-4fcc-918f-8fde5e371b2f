{"version": 3, "names": ["React", "Platform", "StyleSheet", "PlatformPressable", "jsx", "_jsx", "HeaderButtonInternal", "disabled", "onPress", "pressColor", "pressOpacity", "accessibilityLabel", "testID", "style", "href", "children", "ref", "android_ripple", "androidRipple", "styles", "container", "hitSlop", "select", "ios", "undefined", "default", "top", "right", "bottom", "left", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardRef", "displayName", "borderless", "foreground", "OS", "Version", "radius", "create", "flexDirection", "alignItems", "paddingHorizontal", "borderRadius", "opacity"], "sourceRoot": "../../../src", "sources": ["Header/HeaderButton.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,UAAU,QAAQ,cAAc;AAEnD,SAASC,iBAAiB,QAAQ,yBAAsB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAGzD,SAASC,oBAAoBA,CAC3B;EACEC,QAAQ;EACRC,OAAO;EACPC,UAAU;EACVC,YAAY;EACZC,kBAAkB;EAClBC,MAAM;EACNC,KAAK;EACLC,IAAI;EACJC;AACiB,CAAC,EACpBC,GAA4D,EAC5D;EACA,oBACEX,IAAA,CAACF,iBAAiB;IAChBa,GAAG,EAAEA,GAAI;IACTT,QAAQ,EAAEA,QAAS;IACnBO,IAAI,EAAEA,IAAK;IACX,cAAYH,kBAAmB;IAC/BC,MAAM,EAAEA,MAAO;IACfJ,OAAO,EAAEA,OAAQ;IACjBC,UAAU,EAAEA,UAAW;IACvBC,YAAY,EAAEA,YAAa;IAC3BO,cAAc,EAAEC,aAAc;IAC9BL,KAAK,EAAE,CAACM,MAAM,CAACC,SAAS,EAAEb,QAAQ,IAAIY,MAAM,CAACZ,QAAQ,EAAEM,KAAK,CAAE;IAC9DQ,OAAO,EAAEpB,QAAQ,CAACqB,MAAM,CAAC;MACvBC,GAAG,EAAEC,SAAS;MACdC,OAAO,EAAE;QAAEC,GAAG,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAG;IACtD,CAAC,CAAE;IAAAd,QAAA,EAEFA;EAAQ,CACQ,CAAC;AAExB;AAEA,OAAO,MAAMe,YAAY,gBAAG9B,KAAK,CAAC+B,UAAU,CAACzB,oBAAoB,CAAC;AAElEwB,YAAY,CAACE,WAAW,GAAG,cAAc;AAEzC,MAAMd,aAAa,GAAG;EACpBe,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAEjC,QAAQ,CAACkC,EAAE,KAAK,SAAS,IAAIlC,QAAQ,CAACmC,OAAO,IAAI,EAAE;EAC/DC,MAAM,EAAE;AACV,CAAC;AAED,MAAMlB,MAAM,GAAGjB,UAAU,CAACoC,MAAM,CAAC;EAC/BlB,SAAS,EAAE;IACTmB,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,iBAAiB,EAAE,CAAC;IACpB;IACAC,YAAY,EAAE;EAChB,CAAC;EACDnC,QAAQ,EAAE;IACRoC,OAAO,EAAE;EACX;AACF,CAAC,CAAC", "ignoreList": []}