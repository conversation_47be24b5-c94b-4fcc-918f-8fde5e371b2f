{"version": 3, "names": ["React", "contexts", "globalThis", "Map", "getNamedContext", "name", "initialValue", "context", "get", "createContext", "displayName", "set"], "sourceRoot": "../../src", "sources": ["getNamedContext.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,MAAMC,QAAQ,GAAG,uCAAuC;AAOxD;AACAC,UAAU,CAACD,QAAQ,CAAC,GAClBC,UAAU,CAACD,QAAQ,CAAC,IAAI,IAAIE,GAAG,CAA6B,CAAC;AAE/D,OAAO,SAASC,eAAeA,CAC7BC,IAAY,EACZC,YAAe,EACG;EAClB,IAAIC,OAAO,GAAGL,UAAU,CAACD,QAAQ,CAAC,CAACO,GAAG,CAACH,IAAI,CAAC;EAE5C,IAAIE,OAAO,EAAE;IACX,OAAOA,OAAO;EAChB;EAEAA,OAAO,gBAAGP,KAAK,CAACS,aAAa,CAAIH,YAAY,CAAC;EAC9CC,OAAO,CAACG,WAAW,GAAGL,IAAI;EAE1BH,UAAU,CAACD,QAAQ,CAAC,CAACU,GAAG,CAACN,IAAI,EAAEE,OAAO,CAAC;EAEvC,OAAOA,OAAO;AAChB", "ignoreList": []}