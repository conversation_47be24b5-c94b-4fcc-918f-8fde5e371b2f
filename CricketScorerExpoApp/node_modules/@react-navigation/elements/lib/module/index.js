"use strict";

import backIcon from './assets/back-icon.png';
import backIconMask from './assets/back-icon-mask.png';
import clearIcon from './assets/clear-icon.png';
import closeIcon from './assets/close-icon.png';
import searchIcon from './assets/search-icon.png';
export { Background } from "./Background.js";
export { Button } from "./Button.js";
export { getDefaultSidebarWidth } from "./getDefaultSidebarWidth.js";
export { getDefaultHeaderHeight } from "./Header/getDefaultHeaderHeight.js";
export { getHeaderTitle } from "./Header/getHeaderTitle.js";
export { Header } from "./Header/Header.js";
export { HeaderBackButton } from "./Header/HeaderBackButton.js";
export { HeaderBackContext } from "./Header/HeaderBackContext.js";
export { HeaderBackground } from "./Header/HeaderBackground.js";
export { HeaderButton } from "./Header/HeaderButton.js";
export { HeaderHeightContext } from "./Header/HeaderHeightContext.js";
export { HeaderShownContext } from "./Header/HeaderShownContext.js";
export { HeaderTitle } from "./Header/HeaderTitle.js";
export { useHeaderHeight } from "./Header/useHeaderHeight.js";
export { getLabel } from "./Label/getLabel.js";
export { Label } from "./Label/Label.js";
export { MissingIcon } from "./MissingIcon.js";
export { PlatformPressable } from "./PlatformPressable.js";
export { ResourceSavingView } from "./ResourceSavingView.js";
export { SafeAreaProviderCompat } from "./SafeAreaProviderCompat.js";
export { Screen } from "./Screen.js";
export { Text } from "./Text.js";
export const Assets = [backIcon, backIconMask, searchIcon, closeIcon, clearIcon];
export * from "./types.js";
//# sourceMappingURL=index.js.map