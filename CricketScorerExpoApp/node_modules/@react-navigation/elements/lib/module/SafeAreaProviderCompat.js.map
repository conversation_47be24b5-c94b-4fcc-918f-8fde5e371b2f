{"version": 3, "names": ["React", "Dimensions", "Platform", "StyleSheet", "View", "initialWindowMetrics", "SafeAreaFrameContext", "SafeAreaInsetsContext", "SafeAreaProvider", "jsx", "_jsx", "jsxs", "_jsxs", "width", "height", "get", "initialMetrics", "OS", "frame", "x", "y", "insets", "top", "left", "right", "bottom", "SafeAreaProviderCompat", "children", "style", "useContext", "styles", "container", "SafeAreaFrameProvider", "element", "useRef", "set<PERSON>rame", "useState", "useEffect", "current", "rect", "getBoundingClientRect", "timeout", "observer", "ResizeObserver", "entries", "entry", "contentRect", "clearTimeout", "setTimeout", "observe", "disconnect", "Provider", "value", "ref", "absoluteFillObject", "pointerEvents", "visibility", "create", "flex"], "sourceRoot": "../../src", "sources": ["SafeAreaProviderCompat.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,UAAU,EACVC,QAAQ,EAERC,UAAU,EACVC,IAAI,QAEC,cAAc;AACrB,SACEC,oBAAoB,EAEpBC,oBAAoB,EACpBC,qBAAqB,EACrBC,gBAAgB,QACX,gCAAgC;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAOxC,MAAM;EAAEC,KAAK,GAAG,CAAC;EAAEC,MAAM,GAAG;AAAE,CAAC,GAAGb,UAAU,CAACc,GAAG,CAAC,QAAQ,CAAC;;AAE1D;AACA;AACA;AACA,MAAMC,cAAc,GAClBd,QAAQ,CAACe,EAAE,KAAK,KAAK,IAAIZ,oBAAoB,IAAI,IAAI,GACjD;EACEa,KAAK,EAAE;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEP,KAAK;IAAEC;EAAO,CAAC;EACpCO,MAAM,EAAE;IAAEC,GAAG,EAAE,CAAC;IAAEC,IAAI,EAAE,CAAC;IAAEC,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE;AACjD,CAAC,GACDpB,oBAAoB;AAE1B,OAAO,SAASqB,sBAAsBA,CAAC;EAAEC,QAAQ;EAAEC;AAAa,CAAC,EAAE;EACjE,MAAMP,MAAM,GAAGrB,KAAK,CAAC6B,UAAU,CAACtB,qBAAqB,CAAC;EAEtD,IAAIc,MAAM,EAAE;IACV;IACA;IACA;IACA,oBAAOX,IAAA,CAACN,IAAI;MAACwB,KAAK,EAAE,CAACE,MAAM,CAACC,SAAS,EAAEH,KAAK,CAAE;MAAAD,QAAA,EAAEA;IAAQ,CAAO,CAAC;EAClE;EAEA,IAAIzB,QAAQ,CAACe,EAAE,KAAK,KAAK,EAAE;IACzBU,QAAQ,gBACNjB,IAAA,CAACsB,qBAAqB;MAAChB,cAAc,EAAEA,cAAe;MAAAW,QAAA,EACnDA;IAAQ,CACY,CACxB;EACH;EAEA,oBACEjB,IAAA,CAACF,gBAAgB;IAACQ,cAAc,EAAEA,cAAe;IAACY,KAAK,EAAEA,KAAM;IAAAD,QAAA,EAC5DA;EAAQ,CACO,CAAC;AAEvB;;AAEA;AACA;AACA,MAAMK,qBAAqB,GAAGA,CAAC;EAC7BhB,cAAc;EACdW;AAIF,CAAC,KAAK;EACJ,MAAMM,OAAO,GAAGjC,KAAK,CAACkC,MAAM,CAAiB,IAAI,CAAC;EAClD,MAAM,CAAChB,KAAK,EAAEiB,QAAQ,CAAC,GAAGnC,KAAK,CAACoC,QAAQ,CAACpB,cAAc,CAACE,KAAK,CAAC;EAE9DlB,KAAK,CAACqC,SAAS,CAAC,MAAM;IACpB,IAAIJ,OAAO,CAACK,OAAO,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAMC,IAAI,GAAGN,OAAO,CAACK,OAAO,CAACE,qBAAqB,CAAC,CAAC;;IAEpD;IACAL,QAAQ,CAAC;MACPhB,CAAC,EAAEoB,IAAI,CAACpB,CAAC;MACTC,CAAC,EAAEmB,IAAI,CAACnB,CAAC;MACTP,KAAK,EAAE0B,IAAI,CAAC1B,KAAK;MACjBC,MAAM,EAAEyB,IAAI,CAACzB;IACf,CAAC,CAAC;IAEF,IAAI2B,OAAsC;IAE1C,MAAMC,QAAQ,GAAG,IAAIC,cAAc,CAAEC,OAAO,IAAK;MAC/C,MAAMC,KAAK,GAAGD,OAAO,CAAC,CAAC,CAAC;MAExB,IAAIC,KAAK,EAAE;QACT,MAAM;UAAE1B,CAAC;UAAEC,CAAC;UAAEP,KAAK;UAAEC;QAAO,CAAC,GAAG+B,KAAK,CAACC,WAAW;;QAEjD;QACAC,YAAY,CAACN,OAAO,CAAC;QACrBA,OAAO,GAAGO,UAAU,CAAC,MAAM;UACzBb,QAAQ,CAAC;YAAEhB,CAAC;YAAEC,CAAC;YAAEP,KAAK;YAAEC;UAAO,CAAC,CAAC;QACnC,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,CAAC;IAEF4B,QAAQ,CAACO,OAAO,CAAChB,OAAO,CAACK,OAAO,CAAC;IAEjC,OAAO,MAAM;MACXI,QAAQ,CAACQ,UAAU,CAAC,CAAC;MACrBH,YAAY,CAACN,OAAO,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE7B,KAAA,CAACN,oBAAoB,CAAC6C,QAAQ;IAACC,KAAK,EAAElC,KAAM;IAAAS,QAAA,gBAC1CjB,IAAA;MACE2C,GAAG,EAAEpB,OAAQ;MACbL,KAAK,EAAE;QACL,GAAGzB,UAAU,CAACmD,kBAAkB;QAChCC,aAAa,EAAE,MAAM;QACrBC,UAAU,EAAE;MACd;IAAE,CACH,CAAC,EACD7B,QAAQ;EAAA,CACoB,CAAC;AAEpC,CAAC;AAEDD,sBAAsB,CAACV,cAAc,GAAGA,cAAc;AAEtD,MAAMc,MAAM,GAAG3B,UAAU,CAACsD,MAAM,CAAC;EAC/B1B,SAAS,EAAE;IACT2B,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}