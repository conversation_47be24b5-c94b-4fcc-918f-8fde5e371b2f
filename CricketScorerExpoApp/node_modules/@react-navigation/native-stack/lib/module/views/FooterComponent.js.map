{"version": 3, "names": ["React", "ScreenFooter", "jsx", "_jsx", "FooterComponent", "children", "collapsable"], "sourceRoot": "../../../src", "sources": ["views/FooterComponent.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,QAAQ,sBAAsB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAMpD,OAAO,SAASC,eAAeA,CAAC;EAAEC;AAAsB,CAAC,EAAE;EACzD,oBAAOF,IAAA,CAACF,YAAY;IAACK,WAAW,EAAE,KAAM;IAAAD,QAAA,EAAEA;EAAQ,CAAe,CAAC;AACpE", "ignoreList": []}