{"version": 3, "names": ["getHeaderTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useLocale", "useTheme", "Platform", "StyleSheet", "View", "isSearchBarAvailableForCurrentPlatform", "ScreenStackHeaderBackButtonImage", "ScreenStackHeaderCenterView", "ScreenStackHeaderLeftView", "ScreenStackHeaderRightView", "ScreenStackHeaderSearchBarView", "SearchBar", "processFonts", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "useHeaderConfigProps", "headerBackImageSource", "headerBackButtonDisplayMode", "headerBackButtonMenuEnabled", "headerBackTitle", "headerBackTitleStyle", "headerBackVisible", "headerShadowVisible", "headerLargeStyle", "headerLargeTitle", "headerLargeTitleShadowVisible", "headerLargeTitleStyle", "headerBackground", "headerLeft", "headerRight", "headerShown", "headerStyle", "headerBlurEffect", "headerTintColor", "headerTitle", "headerTitleAlign", "headerTitleStyle", "headerTransparent", "headerSearchBarOptions", "headerTopInsetEnabled", "headerBack", "route", "title", "direction", "colors", "fonts", "tintColor", "OS", "primary", "text", "headerBackTitleStyleFlattened", "flatten", "regular", "headerLargeTitleStyleFlattened", "select", "ios", "heavy", "default", "medium", "headerTitleStyleFlattened", "bold", "headerStyleFlattened", "headerLargeStyleFlattened", "backTitleFontFamily", "largeTitleFontFamily", "titleFontFamily", "fontFamily", "backTitleFontSize", "fontSize", "undefined", "titleText", "name", "titleColor", "color", "titleFontSize", "titleFontWeight", "fontWeight", "largeTitleBackgroundColor", "backgroundColor", "largeTitleColor", "largeTitleFontSize", "largeTitleFontWeight", "headerTitleStyleSupported", "headerBackgroundColor", "card", "canGoBack", "headerLeftElement", "label", "href", "headerRightElement", "headerTitleElement", "children", "supportsHeaderSearchBar", "hasHeaderSearchBar", "backButtonInCustomView", "translucent", "isBackButtonDisplayModeAvailable", "parseInt", "Version", "isCenterViewRenderedAndroid", "style", "flex", "source", "backTitle", "backTitleVisible", "backButtonDisplayMode", "blurEffect", "disableBackButtonMenu", "hidden", "hideBackButton", "hideShadow", "largeTitle", "largeTitleHideShadow", "String", "topInsetEnabled"], "sourceRoot": "../../../src", "sources": ["views/useHeaderConfigProps.tsx"], "mappings": ";;AAAA,SAASA,cAAc,EAAEC,WAAW,QAAQ,4BAA4B;AACxE,SAAqBC,SAAS,EAAEC,QAAQ,QAAQ,0BAA0B;AAC1E,SAASC,QAAQ,EAAEC,UAAU,EAAkBC,IAAI,QAAQ,cAAc;AACzE,SACEC,sCAAsC,EACtCC,gCAAgC,EAChCC,2BAA2B,EAC3BC,yBAAyB,EACzBC,0BAA0B,EAC1BC,8BAA8B,EAC9BC,SAAS,QACJ,sBAAsB;AAG7B,SAASC,YAAY,QAAQ,iBAAiB;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,QAAA,IAAAC,SAAA,EAAAC,IAAA,IAAAC,KAAA;AAS/C,OAAO,SAASC,oBAAoBA,CAAC;EACnCC,qBAAqB;EACrBC,2BAA2B;EAC3BC,2BAA2B;EAC3BC,eAAe;EACfC,oBAAoB;EACpBC,iBAAiB;EACjBC,mBAAmB;EACnBC,gBAAgB;EAChBC,gBAAgB;EAChBC,6BAA6B;EAC7BC,qBAAqB;EACrBC,gBAAgB;EAChBC,UAAU;EACVC,WAAW;EACXC,WAAW;EACXC,WAAW;EACXC,gBAAgB;EAChBC,eAAe;EACfC,WAAW;EACXC,gBAAgB;EAChBC,gBAAgB;EAChBC,iBAAiB;EACjBC,sBAAsB;EACtBC,qBAAqB;EACrBC,UAAU;EACVC,KAAK;EACLC;AACK,CAAC,EAAE;EACR,MAAM;IAAEC;EAAU,CAAC,GAAG/C,SAAS,CAAC,CAAC;EACjC,MAAM;IAAEgD,MAAM;IAAEC;EAAM,CAAC,GAAGhD,QAAQ,CAAC,CAAC;EACpC,MAAMiD,SAAS,GACbb,eAAe,KAAKnC,QAAQ,CAACiD,EAAE,KAAK,KAAK,GAAGH,MAAM,CAACI,OAAO,GAAGJ,MAAM,CAACK,IAAI,CAAC;EAE3E,MAAMC,6BAA6B,GACjCnD,UAAU,CAACoD,OAAO,CAAC,CAACN,KAAK,CAACO,OAAO,EAAEhC,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC;EACjE,MAAMiC,8BAA8B,GAClCtD,UAAU,CAACoD,OAAO,CAAC,CACjBrD,QAAQ,CAACwD,MAAM,CAAC;IAAEC,GAAG,EAAEV,KAAK,CAACW,KAAK;IAAEC,OAAO,EAAEZ,KAAK,CAACa;EAAO,CAAC,CAAC,EAC5DhC,qBAAqB,CACtB,CAAC,IAAI,CAAC,CAAC;EACV,MAAMiC,yBAAyB,GAC7B5D,UAAU,CAACoD,OAAO,CAAC,CACjBrD,QAAQ,CAACwD,MAAM,CAAC;IAAEC,GAAG,EAAEV,KAAK,CAACe,IAAI;IAAEH,OAAO,EAAEZ,KAAK,CAACa;EAAO,CAAC,CAAC,EAC3DtB,gBAAgB,CACjB,CAAC,IAAI,CAAC,CAAC;EACV,MAAMyB,oBAAoB,GAAG9D,UAAU,CAACoD,OAAO,CAACpB,WAAW,CAAC,IAAI,CAAC,CAAC;EAClE,MAAM+B,yBAAyB,GAAG/D,UAAU,CAACoD,OAAO,CAAC5B,gBAAgB,CAAC,IAAI,CAAC,CAAC;EAE5E,MAAM,CAACwC,mBAAmB,EAAEC,oBAAoB,EAAEC,eAAe,CAAC,GAChEzD,YAAY,CAAC,CACX0C,6BAA6B,CAACgB,UAAU,EACxCb,8BAA8B,CAACa,UAAU,EACzCP,yBAAyB,CAACO,UAAU,CACrC,CAAC;EAEJ,MAAMC,iBAAiB,GACrB,UAAU,IAAIjB,6BAA6B,GACvCA,6BAA6B,CAACkB,QAAQ,GACtCC,SAAS;EAEf,MAAMC,SAAS,GAAG5E,cAAc,CAAC;IAAEgD,KAAK;IAAER;EAAY,CAAC,EAAEO,KAAK,CAAC8B,IAAI,CAAC;EACpE,MAAMC,UAAU,GACd,OAAO,IAAIb,yBAAyB,GAChCA,yBAAyB,CAACc,KAAK,GAC9BxC,eAAe,IAAIW,MAAM,CAACK,IAAK;EACtC,MAAMyB,aAAa,GACjB,UAAU,IAAIf,yBAAyB,GACnCA,yBAAyB,CAACS,QAAQ,GAClCC,SAAS;EACf,MAAMM,eAAe,GAAGhB,yBAAyB,CAACiB,UAAU;EAE5D,MAAMC,yBAAyB,GAAGf,yBAAyB,CAACgB,eAAe;EAC3E,MAAMC,eAAe,GACnB,OAAO,IAAI1B,8BAA8B,GACrCA,8BAA8B,CAACoB,KAAK,GACpCJ,SAAS;EACf,MAAMW,kBAAkB,GACtB,UAAU,IAAI3B,8BAA8B,GACxCA,8BAA8B,CAACe,QAAQ,GACvCC,SAAS;EACf,MAAMY,oBAAoB,GAAG5B,8BAA8B,CAACuB,UAAU;EAEtE,MAAMM,yBAAoC,GAAG;IAAET,KAAK,EAAED;EAAW,CAAC;EAElE,IAAIb,yBAAyB,CAACO,UAAU,IAAI,IAAI,EAAE;IAChDgB,yBAAyB,CAAChB,UAAU,GAAGP,yBAAyB,CAACO,UAAU;EAC7E;EAEA,IAAIQ,aAAa,IAAI,IAAI,EAAE;IACzBQ,yBAAyB,CAACd,QAAQ,GAAGM,aAAa;EACpD;EAEA,IAAIC,eAAe,IAAI,IAAI,EAAE;IAC3BO,yBAAyB,CAACN,UAAU,GAAGD,eAAe;EACxD;EAEA,MAAMQ,qBAAqB,GACzBtB,oBAAoB,CAACiB,eAAe,KACnCnD,gBAAgB,IAAI,IAAI,IAAIU,iBAAiB,GAC1C,aAAa,GACbO,MAAM,CAACwC,IAAI,CAAC;EAElB,MAAMC,SAAS,GAAG7C,UAAU,IAAI,IAAI;EAEpC,MAAM8C,iBAAiB,GAAG1D,UAAU,GAAG;IACrCkB,SAAS;IACTuC,SAAS;IACTE,KAAK,EAAEpE,eAAe,IAAIqB,UAAU,EAAEE,KAAK;IAC3C;IACA8C,IAAI,EAAEnB;EACR,CAAC,CAAC;EAEF,MAAMoB,kBAAkB,GAAG5D,WAAW,GAAG;IACvCiB,SAAS;IACTuC;EACF,CAAC,CAAC;EAEF,MAAMK,kBAAkB,GACtB,OAAOxD,WAAW,KAAK,UAAU,GAC7BA,WAAW,CAAC;IACVY,SAAS;IACT6C,QAAQ,EAAErB;EACZ,CAAC,CAAC,GACF,IAAI;EAEV,MAAMsB,uBAAuB,GAC3B,OAAO3F,sCAAsC,KAAK,SAAS,GACvDA,sCAAsC;EACtC;EACAH,QAAQ,CAACiD,EAAE,KAAK,KAAK,IAAIxC,SAAS,IAAI,IAAI;EAEhD,MAAMsF,kBAAkB,GACtBD,uBAAuB,IAAItD,sBAAsB,IAAI,IAAI;;EAE3D;AACF;AACA;AACA;AACA;EACE,MAAMwD,sBAAsB,GAC1BzE,iBAAiB,IAChBvB,QAAQ,CAACiD,EAAE,KAAK,SAAS,IACxB2C,kBAAkB,IAAI,IAAI,IAC1BJ,iBAAiB,IAAI,IAAK;EAE9B,MAAMS,WAAW,GACfpE,gBAAgB,IAAI,IAAI,IACxBU,iBAAiB;EACjB;EACC,CAACwD,kBAAkB,IAAIrE,gBAAgB,KACtC1B,QAAQ,CAACiD,EAAE,KAAK,KAAK,IACrBV,iBAAiB,KAAK,KAAM;EAEhC,MAAM2D,gCAAgC;EACpC;EACAlG,QAAQ,CAACiD,EAAE,KAAK,KAAK,IACrBkD,QAAQ,CAACnG,QAAQ,CAACoG,OAAO,EAAE,EAAE,CAAC,IAAI,EAAE;EACpC;EACCnC,mBAAmB,IAAI,IAAI,IAAIA,mBAAmB,KAAK,QAAQ,CAAC,IACjEI,iBAAiB,IAAI,IAAI;EACzB;EACAjD,2BAA2B,KAAK,KAAK;EAEvC,MAAMiF,2BAA2B,GAAGhE,gBAAgB,KAAK,QAAQ;EAEjE,MAAMwD,QAAQ,gBACZ7E,KAAA,CAAAF,SAAA;IAAA+E,QAAA,GACG7F,QAAQ,CAACiD,EAAE,KAAK,KAAK,gBACpBjC,KAAA,CAAAF,SAAA;MAAA+E,QAAA,GACGL,iBAAiB,IAAI,IAAI,gBACxB5E,IAAA,CAACN,yBAAyB;QAAAuF,QAAA,EACvBL;MAAiB,CACO,CAAC,GAC1B,IAAI,EACPI,kBAAkB,IAAI,IAAI,gBACzBhF,IAAA,CAACP,2BAA2B;QAAAwF,QAAA,EACzBD;MAAkB,CACQ,CAAC,GAC5B,IAAI;IAAA,CACR,CAAC,gBAEH5E,KAAA,CAAAF,SAAA;MAAA+E,QAAA,GACGL,iBAAiB,IAAI,IAAI,IAAI,OAAOpD,WAAW,KAAK,UAAU;MAAA;MAC7D;MACA;MACA;MACApB,KAAA,CAACV,yBAAyB;QACxBgG,KAAK,EAAE,CAACD,2BAA2B,GAAG;UAAEE,IAAI,EAAE;QAAE,CAAC,GAAG,IAAK;QAAAV,QAAA,GAExDL,iBAAiB,EACjBnD,gBAAgB,KAAK,QAAQ,GAC5B,OAAOD,WAAW,KAAK,UAAU,gBAC/BxB,IAAA,CAACV,IAAI;UAACoG,KAAK,EAAE;YAAEC,IAAI,EAAE;UAAE,CAAE;UAAAV,QAAA,EAAED;QAAkB,CAAO,CAAC,gBAErDhF,IAAA,CAACV,IAAI;UAACoG,KAAK,EAAE;YAAEC,IAAI,EAAE;UAAE,CAAE;UAAAV,QAAA,eACvBjF,IAAA,CAACf,WAAW;YACVmD,SAAS,EAAEA,SAAU;YACrBsD,KAAK,EAAElB,yBAA0B;YAAAS,QAAA,EAEhCrB;UAAS,CACC;QAAC,CACV,CACP,GACC,IAAI;MAAA,CACiB,CAAC,GAC1B,IAAI,EACP6B,2BAA2B,gBAC1BzF,IAAA,CAACP,2BAA2B;QAAAwF,QAAA,EACzB,OAAOzD,WAAW,KAAK,UAAU,GAChCwD,kBAAkB,gBAElBhF,IAAA,CAACf,WAAW;UACVmD,SAAS,EAAEA,SAAU;UACrBsD,KAAK,EAAElB,yBAA0B;UAAAS,QAAA,EAEhCrB;QAAS,CACC;MACd,CAC0B,CAAC,GAC5B,IAAI;IAAA,CACR,CACH,EACAtD,qBAAqB,KAAKqD,SAAS,gBAClC3D,IAAA,CAACR,gCAAgC;MAACoG,MAAM,EAAEtF;IAAsB,CAAE,CAAC,GACjE,IAAI,EACPyE,kBAAkB,IAAI,IAAI,gBACzB/E,IAAA,CAACL,0BAA0B;MAAAsF,QAAA,EACxBF;IAAkB,CACO,CAAC,GAC3B,IAAI,EACPI,kBAAkB,gBACjBnF,IAAA,CAACJ,8BAA8B;MAAAqF,QAAA,eAC7BjF,IAAA,CAACH,SAAS;QAAA,GAAK+B;MAAsB,CAAG;IAAC,CACX,CAAC,GAC/B,IAAI;EAAA,CACR,CACH;EAED,OAAO;IACLwD,sBAAsB;IACtBhB,eAAe,EAAEK,qBAAqB;IACtCoB,SAAS,EAAEpF,eAAe;IAC1BqF,gBAAgB,EAAER,gCAAgC,GAC9C3B,SAAS,GACTpD,2BAA2B,KAAK,SAAS;IAC7CwF,qBAAqB,EAAET,gCAAgC,GACnD/E,2BAA2B,GAC3BoD,SAAS;IACbN,mBAAmB;IACnBI,iBAAiB;IACjBuC,UAAU,EAAE1E,gBAAgB;IAC5ByC,KAAK,EAAE3B,SAAS;IAChBH,SAAS;IACTgE,qBAAqB,EAAEzF,2BAA2B,KAAK,KAAK;IAC5D0F,MAAM,EAAE9E,WAAW,KAAK,KAAK;IAC7B+E,cAAc,EAAExF,iBAAiB,KAAK,KAAK;IAC3CyF,UAAU,EACRxF,mBAAmB,KAAK,KAAK,IAC7BK,gBAAgB,IAAI,IAAI,IACvBU,iBAAiB,IAAIf,mBAAmB,KAAK,IAAK;IACrDyF,UAAU,EAAEvF,gBAAgB;IAC5BqD,yBAAyB;IACzBE,eAAe;IACff,oBAAoB;IACpBgB,kBAAkB;IAClBC,oBAAoB;IACpB+B,oBAAoB,EAAEvF,6BAA6B,KAAK,KAAK;IAC7DiB,KAAK,EAAE4B,SAAS;IAChBE,UAAU;IACVP,eAAe;IACfS,aAAa;IACbC,eAAe,EAAEsC,MAAM,CAACtC,eAAe,CAAC;IACxCuC,eAAe,EAAE3E,qBAAqB;IACtCwD,WAAW,EAAEA,WAAW,KAAK,IAAI;IACjCJ;EACF,CAAC;AACH", "ignoreList": []}