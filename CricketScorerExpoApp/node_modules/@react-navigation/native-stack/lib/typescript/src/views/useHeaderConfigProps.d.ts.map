{"version": 3, "file": "useHeaderConfigProps.d.ts", "sourceRoot": "", "sources": ["../../../../src/views/useHeaderConfigProps.tsx"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,KAAK,EAAuB,MAAM,0BAA0B,CAAC;AAY3E,OAAO,KAAK,EAAE,4BAA4B,EAAE,MAAM,UAAU,CAAC;AAG7D,KAAK,KAAK,GAAG,4BAA4B,GAAG;IAC1C,qBAAqB,EAAE,OAAO,CAAC;IAC/B,YAAY,EAAE,MAAM,CAAC;IACrB,UAAU,EAAE;QAAE,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;QAAC,IAAI,EAAE,SAAS,CAAA;KAAE,GAAG,SAAS,CAAC;IACxE,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;CACtB,CAAC;AAEF,wBAAgB,oBAAoB,CAAC,EACnC,qBAAqB,EACrB,2BAA2B,EAC3B,2BAA2B,EAC3B,eAAe,EACf,oBAAoB,EACpB,iBAAiB,EACjB,mBAAmB,EACnB,gBAAgB,EAChB,gBAAgB,EAChB,6BAA6B,EAC7B,qBAAqB,EACrB,gBAAgB,EAChB,UAAU,EACV,WAAW,EACX,WAAW,EACX,WAAW,EACX,gBAAgB,EAChB,eAAe,EACf,WAAW,EACX,gBAAgB,EAChB,gBAAgB,EAChB,iBAAiB,EACjB,sBAAsB,EACtB,qBAAqB,EACrB,UAAU,EACV,KAAK,EACL,KAAK,GACN,EAAE,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAyPP"}