{"version": 3, "file": "StaticNavigation.d.ts", "sourceRoot": "", "sources": ["../../../src/StaticNavigation.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAC;AAChF,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAG/B,OAAO,KAAK,EACV,uBAAuB,EACvB,YAAY,EACZ,kBAAkB,EAClB,qBAAqB,EACrB,oBAAoB,EACpB,UAAU,EACV,oBAAoB,EACpB,gBAAgB,EAChB,gBAAgB,EACjB,MAAM,SAAS,CAAC;AAGjB;;;GAGG;AACH,KAAK,QAAQ,CAAC,CAAC,IAAI;KAAG,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CAAE,GAAG,EAAE,CAAC;AAEjD;;;GAGG;AACH,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC;AAEhD;;;GAGG;AACH,KAAK,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,SAAS,CAC7E,CAAC,EAAE,MAAM,CAAC,KACP,IAAI,GACL,CAAC,GACD,KAAK,CAAC;AAEV,KAAK,kBAAkB,CAAC,CAAC,IAAI,OAAO,SAAS,CAAC,GAAG,SAAS,GAAG,CAAC,CAAC;AAE/D,KAAK,wBAAwB,CAAC,CAAC,IAAI,CAAC,SAAS;IAC3C,MAAM,EAAE,KAAK,CAAC,aAAa,CAAC;QAAE,KAAK,EAAE;YAAE,MAAM,EAAE,MAAM,CAAC,CAAA;SAAE,CAAA;KAAE,CAAC,CAAC;CAC7D,GACG,CAAC,GACD,CAAC,SAAS,KAAK,CAAC,aAAa,CAAC;IAAE,KAAK,EAAE;QAAE,MAAM,EAAE,MAAM,CAAC,CAAA;KAAE,CAAA;CAAE,CAAC,GAC3D,CAAC,GACD,SAAS,CAAC;AAEhB,KAAK,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS;IAAE,MAAM,EAAE,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;CAAE,GAC3E,qBAAqB,CAAC,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,SAAS,GAC/D,CAAC,SAAS,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GACvC,qBAAqB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,GACrD,kBAAkB,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC;AAEtD,KAAK,mBAAmB,CAAC,OAAO,IAAI;KACjC,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;CACxD,CAAC;AAEF,KAAK,kBAAkB,CACrB,MAAM,SACF,QAAQ,CAAC;IACP,CAAC,GAAG,EAAE,MAAM,GAAG;QACb,OAAO,EAAE,mBAAmB,CAC1B,aAAa,EACb,eAAe,EACf,EAAE,EACF,YAAY,EACZ,GAAG,CACJ,CAAC;KACH,CAAC;CACH,CAAC,GACF,SAAS,IACX,MAAM,SAAS;IACjB,CAAC,GAAG,EAAE,MAAM,GAAG;QACb,OAAO,EAAE,mBAAmB,CAC1B,aAAa,EACb,eAAe,EACf,EAAE,EACF,YAAY,EACZ,GAAG,CACJ,CAAC;KACH,CAAC;CACH,GACG,mBAAmB,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GACzE,EAAE,CAAC;AAEP,KAAK,iBAAiB,CACpB,SAAS,SAAS,aAAa,EAC/B,SAAS,SAAS,MAAM,SAAS,EACjC,KAAK,SAAS,eAAe,EAC7B,aAAa,SAAS,EAAE,EACxB,QAAQ,SAAS,YAAY,EAC7B,UAAU,IACR,gBAAgB,CAClB,SAAS,EACT,SAAS,EACT,KAAK,EACL,aAAa,EACb,QAAQ,EACR,UAAU,CACX,GACC,oBAAoB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AAE7C,MAAM,MAAM,mBAAmB,CAC7B,SAAS,SAAS,aAAa,EAC/B,KAAK,SAAS,eAAe,EAC7B,aAAa,SAAS,EAAE,EACxB,QAAQ,SAAS,YAAY,EAC7B,cAAc,SAAS,kBAAkB,CAAC,SAAS,CAAC,IAClD;KACD,SAAS,IAAI,MAAM,SAAS,GACzB,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,GACxB,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAC/B,CAAC,IAAI,CACH,iBAAiB,CACf,SAAS,EACT,SAAS,EACT,KAAK,EACL,aAAa,EACb,QAAQ,EACR,cAAc,CAAC,SAAS,CAAC,CAC1B,EACD,MAAM,GAAG,WAAW,GAAG,cAAc,GAAG,UAAU,CACnD,GAAG;QACF;;;;;;;;;;;WAWG;QACH,EAAE,CAAC,EAAE,MAAM,OAAO,CAAC;QACnB;;;;;;;;;;;WAWG;QACH,OAAO,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC;QACzC;;WAEG;QACH,MAAM,EAAE,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;KACpE,CAAC;CACP,CAAC;AAEF,MAAM,MAAM,iBAAiB,CAC3B,SAAS,SAAS,aAAa,EAC/B,KAAK,SAAS,eAAe,EAC7B,aAAa,SAAS,EAAE,EACxB,QAAQ,SAAS,YAAY,EAC7B,cAAc,SAAS,kBAAkB,CAAC,SAAS,CAAC,IAClD,IAAI,CACN,gBAAgB,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,CAAC,MAAM,SAAS,CAAC,CAAC,EAC3E,SAAS,GAAG,UAAU,CACvB,GAAG;IACF;;;OAGG;IACH,EAAE,CAAC,EAAE,MAAM,OAAO,CAAC;IACnB;;OAEG;IACH,OAAO,EAAE,mBAAmB,CAC1B,SAAS,EACT,KAAK,EACL,aAAa,EACb,QAAQ,EACR,cAAc,CACf,CAAC;CACH,CAAC;AAEF,MAAM,MAAM,YAAY,CAAC,GAAG,SAAS,oBAAoB,IACvD,oBAAoB,CAClB,GAAG,CAAC,WAAW,CAAC,EAChB,GAAG,CAAC,aAAa,CAAC,EAClB,GAAG,CAAC,OAAO,CAAC,EACZ,GAAG,CAAC,eAAe,CAAC,EACpB,GAAG,CAAC,UAAU,CAAC,EACf,GAAG,CAAC,gBAAgB,CAAC,EACrB,GAAG,CAAC,WAAW,CAAC,CACjB,CAAC;AAEJ,KAAK,oBAAoB,CACvB,SAAS,SAAS,aAAa,EAC/B,WAAW,SAAS,MAAM,GAAG,SAAS,EACtC,KAAK,SAAS,eAAe,EAC7B,aAAa,SAAS,EAAE,EACxB,QAAQ,SAAS,YAAY,EAC7B,cAAc,SAAS,kBAAkB,CAAC,SAAS,CAAC,EACpD,SAAS,SAAS,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,IACxC,IAAI,CACN,IAAI,CACF,KAAK,CAAC,cAAc,CAAC,SAAS,CAAC,EAC/B,MAAM,uBAAuB,CAC3B,aAAa,EACb,MAAM,GAAG,SAAS,EAClB,eAAe,EACf,EAAE,EACF,YAAY,EACZ,cAAc,CAAC,MAAM,SAAS,CAAC,CAChC,CACF,GACC,uBAAuB,CACrB,SAAS,EACT,WAAW,EACX,KAAK,EACL,aAAa,EACb,QAAQ,EACR,cAAc,CAAC,MAAM,SAAS,CAAC,CAChC,EACH,SAAS,GAAG,UAAU,CACvB,GACC,CACI;IACE;;OAEG;IACH,OAAO,EAAE,mBAAmB,CAC1B,SAAS,EACT,KAAK,EACL,aAAa,EACb,QAAQ,EACR,cAAc,CACf,CAAC;IACF;;OAEG;IACH,MAAM,CAAC,EAAE;QACP,CAAC,GAAG,EAAE,MAAM,GAAG,iBAAiB,CAC9B,SAAS,EACT,KAAK,EACL,aAAa,EACb,QAAQ,EACR,cAAc,CACf,CAAC;KACH,CAAC;CACH,GACD;IACE;;OAEG;IACH,OAAO,CAAC,EAAE,mBAAmB,CAC3B,SAAS,EACT,KAAK,EACL,aAAa,EACb,QAAQ,EACR,cAAc,CACf,CAAC;IACF;;OAEG;IACH,MAAM,EAAE;QACN,CAAC,GAAG,EAAE,MAAM,GAAG,iBAAiB,CAC9B,SAAS,EACT,KAAK,EACL,aAAa,EACb,QAAQ,EACR,cAAc,CACf,CAAC;KACH,CAAC;CACH,CACJ,CAAC;AAEJ;;;GAGG;AACH,MAAM,MAAM,iBAAiB,CAAC,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,SAAS,IAAI;IAC7E,KAAK,EAAE;QACL,MAAM,EAAE,CAAC,CAAC;KACX,CAAC;CACH,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,eAAe,CACzB,CAAC,SAAS;IACR,QAAQ,CAAC,MAAM,EAAE;QACf,QAAQ,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QACvC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAChB,CAAC,GAAG,EAAE,MAAM,GAAG;gBACb,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;aAC9B,CAAC;SACH,CAAC;KACH,CAAC;CACH,IACC,QAAQ,CACV,mBAAmB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC,GACzC,kBAAkB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAC5C,CAAC;AAEF,MAAM,MAAM,gBAAgB,CAAC,cAAc,EAAE,UAAU,EAAE,WAAW,IAAI;IACtE,SAAS,EAAE,KAAK,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;IAC/C,KAAK,EAAE,KAAK,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IACvC,MAAM,EAAE,KAAK,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;IACzC,MAAM,EAAE,YAAY,CAAC,oBAAoB,CAAC,CAAC;CAC5C,CAAC;AA0EF;;;;;;GAMG;AACH,wBAAgB,kCAAkC,CAChD,IAAI,EAAE,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EACrC,WAAW,EAAE,MAAM,GAClB,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC,CAsDzB;AAED,KAAK,iBAAiB,GAAG;IACvB,MAAM,EAAE;QACN,gBAAgB,CAAC,EAAE,MAAM,CAAC;QAC1B,OAAO,CAAC,EAAE,mBAAmB,CAC3B,aAAa,EACb,eAAe,EACf,EAAE,EACF,YAAY,EACZ,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CACxB,CAAC;QACF,MAAM,CAAC,EAAE;YACP,CAAC,GAAG,EAAE,MAAM,GAAG;gBACb,OAAO,EAAE,mBAAmB,CAC1B,aAAa,EACb,eAAe,EACf,EAAE,EACF,YAAY,EACZ,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CACxB,CAAC;aACH,CAAC;SACH,CAAC;KACH,CAAC;CACH,CAAC;AAEF;;;;;;;;;;;;;;;;;;GAkBG;AACH,wBAAgB,mCAAmC,CACjD,IAAI,EAAE,iBAAiB,EACvB,OAAO,CAAC,EAAE;IACR,gBAAgB,CAAC,EAAE,MAAM,CAAC;CAC3B,EACD,IAAI,CAAC,EAAE,OAAO,kBAyJf"}