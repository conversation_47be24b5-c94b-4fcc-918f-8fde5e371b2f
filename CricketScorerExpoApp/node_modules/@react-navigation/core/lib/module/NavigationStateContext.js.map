{"version": 3, "names": ["React", "MISSING_CONTEXT_ERROR", "NavigationStateContext", "createContext", "isDefault", "<PERSON><PERSON><PERSON>", "Error", "<PERSON><PERSON><PERSON>", "getState", "setState", "getIsInitial"], "sourceRoot": "../../src", "sources": ["NavigationStateContext.tsx"], "mappings": ";;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,MAAMC,qBAAqB,GACzB,wKAAwK;AAE1K,OAAO,MAAMC,sBAAsB,gBAAGF,KAAK,CAACG,aAAa,CActD;EACDC,SAAS,EAAE,IAAI;EAEf,IAAIC,MAAMA,CAAA,EAAQ;IAChB,MAAM,IAAIC,KAAK,CAACL,qBAAqB,CAAC;EACxC,CAAC;EACD,IAAIM,MAAMA,CAAA,EAAQ;IAChB,MAAM,IAAID,KAAK,CAACL,qBAAqB,CAAC;EACxC,CAAC;EACD,IAAIO,QAAQA,CAAA,EAAQ;IAClB,MAAM,IAAIF,KAAK,CAACL,qBAAqB,CAAC;EACxC,CAAC;EACD,IAAIQ,QAAQA,CAAA,EAAQ;IAClB,MAAM,IAAIH,KAAK,CAACL,qBAAqB,CAAC;EACxC,CAAC;EACD,IAAIS,YAAYA,CAAA,EAAQ;IACtB,MAAM,IAAIJ,KAAK,CAACL,qBAAqB,CAAC;EACxC;AACF,CAAC,CAAC", "ignoreList": []}