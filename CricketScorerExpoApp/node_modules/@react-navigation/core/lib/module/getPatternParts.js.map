{"version": 3, "names": ["getPatternParts", "path", "parts", "current", "segment", "isRegex", "isParam", "regexInnerParens", "i", "length", "char", "Error", "regex", "param", "optional", "replace", "push", "params", "map", "part", "filter", "Boolean", "index", "entries", "indexOf"], "sourceRoot": "../../src", "sources": ["getPatternParts.tsx"], "mappings": ";;AAOA;AACA;AACA;AACA,OAAO,SAASA,eAAeA,CAACC,IAAY,EAAiB;EAC3D,MAAMC,KAAoB,GAAG,EAAE;EAE/B,IAAIC,OAAoB,GAAG;IAAEC,OAAO,EAAE;EAAG,CAAC;EAE1C,IAAIC,OAAO,GAAG,KAAK;EACnB,IAAIC,OAAO,GAAG,KAAK;EACnB,IAAIC,gBAAgB,GAAG,CAAC;;EAExB;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIP,IAAI,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAME,IAAI,GAAGT,IAAI,CAACO,CAAC,CAAC;IAEpB,IAAIE,IAAI,IAAI,IAAI,EAAE;MAChBP,OAAO,CAACC,OAAO,IAAIM,IAAI;IACzB;IAEA,IAAIA,IAAI,KAAK,GAAG,EAAE;MAChB;MACA,IAAIP,OAAO,CAACC,OAAO,KAAK,GAAG,EAAE;QAC3BE,OAAO,GAAG,IAAI;MAChB,CAAC,MAAM,IAAI,CAACD,OAAO,EAAE;QACnB,MAAM,IAAIM,KAAK,CACb,uDAAuDV,IAAI,EAC7D,CAAC;MACH;IACF,CAAC,MAAM,IAAIS,IAAI,KAAK,GAAG,EAAE;MACvB,IAAIJ,OAAO,EAAE;QACX,IAAID,OAAO,EAAE;UACX;UACAE,gBAAgB,EAAE;QACpB,CAAC,MAAM;UACLF,OAAO,GAAG,IAAI;QAChB;MACF,CAAC,MAAM;QACL,MAAM,IAAIM,KAAK,CACb,kDAAkDV,IAAI,EACxD,CAAC;MACH;IACF,CAAC,MAAM,IAAIS,IAAI,KAAK,GAAG,EAAE;MACvB,IAAIJ,OAAO,IAAID,OAAO,EAAE;QACtB,IAAIE,gBAAgB,EAAE;UACpB;UACAA,gBAAgB,EAAE;UAClBJ,OAAO,CAACS,KAAK,IAAIF,IAAI;QACvB,CAAC,MAAM;UACLL,OAAO,GAAG,KAAK;UACfC,OAAO,GAAG,KAAK;QACjB;MACF,CAAC,MAAM;QACL,MAAM,IAAIK,KAAK,CACb,kDAAkDV,IAAI,EACxD,CAAC;MACH;IACF,CAAC,MAAM,IAAIS,IAAI,KAAK,GAAG,EAAE;MACvB,IAAIP,OAAO,CAACU,KAAK,EAAE;QACjBP,OAAO,GAAG,KAAK;QAEfH,OAAO,CAACW,QAAQ,GAAG,IAAI;MACzB,CAAC,MAAM;QACL,MAAM,IAAIH,KAAK,CACb,kDAAkDV,IAAI,EACxD,CAAC;MACH;IACF,CAAC,MAAM,IAAIS,IAAI,IAAI,IAAI,IAAKA,IAAI,KAAK,GAAG,IAAI,CAACL,OAAQ,EAAE;MACrDC,OAAO,GAAG,KAAK;;MAEf;MACAH,OAAO,CAACC,OAAO,GAAGD,OAAO,CAACC,OAAO,CAACW,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MAEpD,IAAIZ,OAAO,CAACC,OAAO,KAAK,EAAE,EAAE;QAC1B;MACF;MAEA,IAAID,OAAO,CAACU,KAAK,EAAE;QACjBV,OAAO,CAACU,KAAK,GAAGV,OAAO,CAACU,KAAK,CAACE,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;MACjD;MAEA,IAAIZ,OAAO,CAACS,KAAK,EAAE;QACjBT,OAAO,CAACS,KAAK,GAAGT,OAAO,CAACS,KAAK,CAACG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MACrE;MAEAb,KAAK,CAACc,IAAI,CAACb,OAAO,CAAC;MAEnB,IAAIO,IAAI,IAAI,IAAI,EAAE;QAChB;MACF;MAEAP,OAAO,GAAG;QAAEC,OAAO,EAAE;MAAG,CAAC;IAC3B;IAEA,IAAIC,OAAO,EAAE;MACXF,OAAO,CAACS,KAAK,GAAGT,OAAO,CAACS,KAAK,IAAI,EAAE;MACnCT,OAAO,CAACS,KAAK,IAAIF,IAAI;IACvB;IAEA,IAAIJ,OAAO,IAAI,CAACD,OAAO,EAAE;MACvBF,OAAO,CAACU,KAAK,GAAGV,OAAO,CAACU,KAAK,IAAI,EAAE;MACnCV,OAAO,CAACU,KAAK,IAAIH,IAAI;IACvB;EACF;EAEA,IAAIL,OAAO,EAAE;IACX,MAAM,IAAIM,KAAK,CAAC,uCAAuCV,IAAI,EAAE,CAAC;EAChE;EAEA,MAAMgB,MAAM,GAAGf,KAAK,CAACgB,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACN,KAAK,CAAC,CAACO,MAAM,CAACC,OAAO,CAAC;EAE9D,KAAK,MAAM,CAACC,KAAK,EAAET,KAAK,CAAC,IAAII,MAAM,CAACM,OAAO,CAAC,CAAC,EAAE;IAC7C,IAAIN,MAAM,CAACO,OAAO,CAACX,KAAK,CAAC,KAAKS,KAAK,EAAE;MACnC,MAAM,IAAIX,KAAK,CAAC,yBAAyBE,KAAK,oBAAoBZ,IAAI,EAAE,CAAC;IAC3E;EACF;EAEA,OAAOC,KAAK;AACd", "ignoreList": []}