{"version": 3, "names": ["React", "NavigationBuilderContext", "NavigationRouteContext", "VISITED_ROUTE_KEYS", "Symbol", "shouldPreventRemove", "emitter", "beforeRemoveListeners", "currentRoutes", "nextRoutes", "action", "nextR<PERSON>e<PERSON>eys", "map", "route", "key", "removedRoutes", "filter", "includes", "reverse", "visitedRouteKeys", "Set", "beforeRemoveAction", "has", "isPrevented", "add", "event", "emit", "type", "target", "data", "canPreventDefault", "defaultPrevented", "useOnPreventRemove", "getState", "addKeyedListener", "useContext", "routeKey", "useEffect", "state", "routes"], "sourceRoot": "../../src", "sources": ["useOnPreventRemove.tsx"], "mappings": ";;AAIA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAEEC,wBAAwB,QACnB,+BAA4B;AACnC,SAASC,sBAAsB,QAAQ,6BAA0B;AAUjE,MAAMC,kBAAkB,GAAGC,MAAM,CAAC,oBAAoB,CAAC;AAEvD,OAAO,MAAMC,mBAAmB,GAAGA,CACjCC,OAAkD,EAClDC,qBAA4E,EAC5EC,aAAgC,EAChCC,UAA0C,EAC1CC,MAAwB,KACrB;EACH,MAAMC,aAAa,GAAGF,UAAU,CAACG,GAAG,CAAEC,KAAK,IAAKA,KAAK,CAACC,GAAG,CAAC;;EAE1D;EACA,MAAMC,aAAa,GAAGP,aAAa,CAChCQ,MAAM,CAAEH,KAAK,IAAK,CAACF,aAAa,CAACM,QAAQ,CAACJ,KAAK,CAACC,GAAG,CAAC,CAAC,CACrDI,OAAO,CAAC,CAAC;EAEZ,MAAMC,gBAA6B;EACjC;EACAT,MAAM,CAACP,kBAAkB,CAAC,IAAI,IAAIiB,GAAG,CAAS,CAAC;EAEjD,MAAMC,kBAAkB,GAAG;IACzB,GAAGX,MAAM;IACT,CAACP,kBAAkB,GAAGgB;EACxB,CAAC;EAED,KAAK,MAAMN,KAAK,IAAIE,aAAa,EAAE;IACjC,IAAII,gBAAgB,CAACG,GAAG,CAACT,KAAK,CAACC,GAAG,CAAC,EAAE;MACnC;MACA;IACF;;IAEA;IACA,MAAMS,WAAW,GAAGhB,qBAAqB,CAACM,KAAK,CAACC,GAAG,CAAC,GAAGO,kBAAkB,CAAC;IAE1E,IAAIE,WAAW,EAAE;MACf,OAAO,IAAI;IACb;IAEAJ,gBAAgB,CAACK,GAAG,CAACX,KAAK,CAACC,GAAG,CAAC;IAE/B,MAAMW,KAAK,GAAGnB,OAAO,CAACoB,IAAI,CAAC;MACzBC,IAAI,EAAE,cAAc;MACpBC,MAAM,EAAEf,KAAK,CAACC,GAAG;MACjBe,IAAI,EAAE;QAAEnB,MAAM,EAAEW;MAAmB,CAAC;MACpCS,iBAAiB,EAAE;IACrB,CAAC,CAAC;IAEF,IAAIL,KAAK,CAACM,gBAAgB,EAAE;MAC1B,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd,CAAC;AAED,OAAO,SAASC,kBAAkBA,CAAC;EACjCC,QAAQ;EACR3B,OAAO;EACPC;AACO,CAAC,EAAE;EACV,MAAM;IAAE2B;EAAiB,CAAC,GAAGlC,KAAK,CAACmC,UAAU,CAAClC,wBAAwB,CAAC;EACvE,MAAMY,KAAK,GAAGb,KAAK,CAACmC,UAAU,CAACjC,sBAAsB,CAAC;EACtD,MAAMkC,QAAQ,GAAGvB,KAAK,EAAEC,GAAG;EAE3Bd,KAAK,CAACqC,SAAS,CAAC,MAAM;IACpB,IAAID,QAAQ,EAAE;MACZ,OAAOF,gBAAgB,GAAG,cAAc,EAAEE,QAAQ,EAAG1B,MAAM,IAAK;QAC9D,MAAM4B,KAAK,GAAGL,QAAQ,CAAC,CAAC;QAExB,OAAO5B,mBAAmB,CACxBC,OAAO,EACPC,qBAAqB,EACrB+B,KAAK,CAACC,MAAM,EACZ,EAAE,EACF7B,MACF,CAAC;MACH,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACwB,gBAAgB,EAAE3B,qBAAqB,EAAED,OAAO,EAAE2B,QAAQ,EAAEG,QAAQ,CAAC,CAAC;AAC5E", "ignoreList": []}