{"version": 3, "names": ["BaseNavigationContainer", "createNavigationContainerRef", "createNavigatorFactory", "CurrentRenderContext", "findFocusedRoute", "getActionFromState", "getFocusedRouteNameFromRoute", "getPathFromState", "getStateFromPath", "NavigationContainerRefContext", "NavigationContext", "NavigationHelpersContext", "NavigationIndependentTree", "NavigationRouteContext", "PreventRemoveContext", "PreventRemoveProvider", "createComponentForStaticNavigation", "createPathConfigForStaticNavigation", "ThemeContext", "ThemeProvider", "useTheme", "useFocusEffect", "useIsFocused", "useNavigation", "useNavigationBuilder", "useNavigationContainerRef", "useNavigationIndependentTree", "useNavigationState", "usePreventRemove", "usePreventRemoveContext", "useRoute", "useStateForPath", "validatePathConfig"], "sourceRoot": "../../src", "sources": ["index.tsx"], "mappings": ";;AAAA,SAASA,uBAAuB,QAAQ,8BAA2B;AACnE,SAASC,4BAA4B,QAAQ,mCAAgC;AAC7E,SAASC,sBAAsB,QAAQ,6BAA0B;AACjE,SAASC,oBAAoB,QAAQ,2BAAwB;AAC7D,SAASC,gBAAgB,QAAQ,uBAAoB;AACrD,SAASC,kBAAkB,QAAQ,yBAAsB;AACzD,SAASC,4BAA4B,QAAQ,mCAAgC;AAC7E,SAASC,gBAAgB,QAAQ,uBAAoB;AACrD,SAASC,gBAAgB,QAAQ,uBAAoB;AACrD,SAASC,6BAA6B,QAAQ,oCAAiC;AAC/E,SAASC,iBAAiB,QAAQ,wBAAqB;AACvD,SAASC,wBAAwB,QAAQ,+BAA4B;AACrE,SAASC,yBAAyB,QAAQ,gCAA6B;AACvE,SAASC,sBAAsB,QAAQ,6BAA0B;AACjE,SAASC,oBAAoB,QAAQ,2BAAwB;AAC7D,SAASC,qBAAqB,QAAQ,4BAAyB;AAC/D,SACEC,kCAAkC,EAClCC,mCAAmC,QAO9B,uBAAoB;AAC3B,SAASC,YAAY,QAAQ,2BAAwB;AACrD,SAASC,aAAa,QAAQ,4BAAyB;AACvD,SAASC,QAAQ,QAAQ,uBAAoB;AAC7C,cAAc,YAAS;AACvB,SAASC,cAAc,QAAQ,qBAAkB;AACjD,SAASC,YAAY,QAAQ,mBAAgB;AAC7C,SAASC,aAAa,QAAQ,oBAAiB;AAC/C,SAASC,oBAAoB,QAAQ,2BAAwB;AAC7D,SAASC,yBAAyB,QAAQ,gCAA6B;AACvE,SAASC,4BAA4B,QAAQ,mCAAgC;AAC7E,SAASC,kBAAkB,QAAQ,yBAAsB;AACzD,SAASC,gBAAgB,QAAQ,uBAAoB;AACrD,SAASC,uBAAuB,QAAQ,8BAA2B;AACnE,SAASC,QAAQ,QAAQ,eAAY;AACrC,SAASC,eAAe,QAAQ,sBAAmB;AACnD,SAASC,kBAAkB,QAAQ,yBAAsB;AACzD,cAAc,2BAA2B", "ignoreList": []}