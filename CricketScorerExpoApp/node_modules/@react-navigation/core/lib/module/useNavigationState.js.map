{"version": 3, "names": ["React", "useSyncExternalStoreWithSelector", "useNavigation", "useNavigationState", "selector", "navigation", "subscribe", "useCallback", "callback", "unsubscribe", "addListener", "value", "getState"], "sourceRoot": "../../src", "sources": ["useNavigationState.tsx"], "mappings": ";;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,gCAAgC,QAAQ,uCAAuC;AAGxF,SAASC,aAAa,QAAQ,oBAAiB;AAM/C;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAChCC,QAAgC,EAC7B;EACH,MAAMC,UAAU,GAAGH,aAAa,CAA4B,CAAC;EAE7D,MAAMI,SAAS,GAAGN,KAAK,CAACO,WAAW,CAChCC,QAAoB,IAAK;IACxB,MAAMC,WAAW,GAAGJ,UAAU,CAACK,WAAW,CAAC,OAAO,EAAEF,QAAQ,CAAC;IAE7D,OAAOC,WAAW;EACpB,CAAC,EACD,CAACJ,UAAU,CACb,CAAC;EAED,MAAMM,KAAK,GAAGV,gCAAgC,CAC5CK,SAAS,EACTD,UAAU,CAACO,QAAQ,EACnBP,UAAU,CAACO,QAAQ,EACnBR,QACF,CAAC;EAED,OAAOO,KAAK;AACd", "ignoreList": []}