{"version": 3, "names": ["React", "NavigationContainerRefContext", "NavigationContext", "useNavigation", "root", "useContext", "navigation", "undefined", "Error"], "sourceRoot": "../../src", "sources": ["useNavigation.tsx"], "mappings": ";;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,6BAA6B,QAAQ,oCAAiC;AAC/E,SAASC,iBAAiB,QAAQ,wBAAqB;AAGvD;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAAA,EAItB;EACL,MAAMC,IAAI,GAAGJ,KAAK,CAACK,UAAU,CAACJ,6BAA6B,CAAC;EAC5D,MAAMK,UAAU,GAAGN,KAAK,CAACK,UAAU,CAACH,iBAAiB,CAAC;EAEtD,IAAII,UAAU,KAAKC,SAAS,IAAIH,IAAI,KAAKG,SAAS,EAAE;IAClD,MAAM,IAAIC,KAAK,CACb,kFACF,CAAC;EACH;;EAEA;EACA,OAAQF,UAAU,IAAIF,IAAI;AAC5B", "ignoreList": []}