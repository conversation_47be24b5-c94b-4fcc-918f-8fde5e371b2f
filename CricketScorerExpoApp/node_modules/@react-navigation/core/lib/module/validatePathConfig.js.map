{"version": 3, "names": ["formatToList", "items", "Object", "entries", "map", "key", "value", "join", "validatePathConfig", "config", "root", "validation", "path", "initialRouteName", "screens", "alias", "exact", "stringify", "parse", "Error", "JSON", "validationErrors", "fromEntries", "keys", "type", "undefined", "Array", "isArray", "filter", "Boolean", "length", "includes", "for<PERSON>ach", "_"], "sourceRoot": "../../src", "sources": ["validatePathConfig.tsx"], "mappings": ";;AAAA,MAAMA,YAAY,GAAIC,KAA6B,IACjDC,MAAM,CAACC,OAAO,CAACF,KAAK,CAAC,CAClBG,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK,KAAKD,GAAG,KAAKC,KAAK,GAAG,CAAC,CAC5CC,IAAI,CAAC,IAAI,CAAC;AAEf,OAAO,SAASC,kBAAkBA,CAACC,MAAe,EAAEC,IAAI,GAAG,IAAI,EAAE;EAC/D,MAAMC,UAAU,GAAG;IACjBC,IAAI,EAAE,QAAQ;IACdC,gBAAgB,EAAE,QAAQ;IAC1BC,OAAO,EAAE,QAAQ;IACjB,IAAIJ,IAAI,GACJ,IAAI,GACJ;MACEK,KAAK,EAAE,OAAO;MACdC,KAAK,EAAE,SAAS;MAChBC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE;IACT,CAAC;EACP,CAAC;EAED,IAAI,OAAOT,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,EAAE;IACjD,MAAM,IAAIU,KAAK,CACb,uDAAuDC,IAAI,CAACH,SAAS,CACnER,MACF,CAAC,GACH,CAAC;EACH;EAEA,MAAMY,gBAAgB,GAAGnB,MAAM,CAACoB,WAAW,CACzCpB,MAAM,CAACqB,IAAI,CAACd,MAAM,CAAC,CAChBL,GAAG,CAAEC,GAAG,IAAK;IACZ,IAAIA,GAAG,IAAIM,UAAU,EAAE;MACrB,MAAMa,IAAI,GAAGb,UAAU,CAACN,GAAG,CAA4B;MACvD;MACA,MAAMC,KAAK,GAAGG,MAAM,CAACJ,GAAG,CAAC;MAEzB,IAAIC,KAAK,KAAKmB,SAAS,EAAE;QACvB,IAAID,IAAI,KAAK,OAAO,EAAE;UACpB,IAAI,CAACE,KAAK,CAACC,OAAO,CAACrB,KAAK,CAAC,EAAE;YACzB,OAAO,CAACD,GAAG,EAAE,0BAA0B,OAAOC,KAAK,GAAG,CAAC;UACzD;QACF,CAAC,MAAM,IAAI,OAAOA,KAAK,KAAKkB,IAAI,EAAE;UAChC,OAAO,CAACnB,GAAG,EAAE,aAAamB,IAAI,WAAW,OAAOlB,KAAK,GAAG,CAAC;QAC3D;MACF;IACF,CAAC,MAAM;MACL,OAAO,CAACD,GAAG,EAAE,YAAY,CAAC;IAC5B;IAEA,OAAO,IAAI;EACb,CAAC,CAAC,CACDuB,MAAM,CAACC,OAAO,CACnB,CAAC;EAED,IAAI3B,MAAM,CAACqB,IAAI,CAACF,gBAAgB,CAAC,CAACS,MAAM,EAAE;IACxC,MAAM,IAAIX,KAAK,CACb,mDAAmDnB,YAAY,CAC7DqB,gBACF,CAAC,uDAAuDrB,YAAY,CAClEW,UACF,CAAC,kOACH,CAAC;EACH;EAEA,IACED,IAAI,IACJ,MAAM,IAAID,MAAM,IAChB,OAAOA,MAAM,CAACG,IAAI,KAAK,QAAQ,IAC/BH,MAAM,CAACG,IAAI,CAACmB,QAAQ,CAAC,GAAG,CAAC,EACzB;IACA,MAAM,IAAIZ,KAAK,CACb,uBAAuBV,MAAM,CAACG,IAAI,kFACpC,CAAC;EACH;EAEA,IAAI,SAAS,IAAIH,MAAM,IAAIA,MAAM,CAACK,OAAO,EAAE;IACzCZ,MAAM,CAACC,OAAO,CAACM,MAAM,CAACK,OAAO,CAAC,CAACkB,OAAO,CAAC,CAAC,CAACC,CAAC,EAAE3B,KAAK,CAAC,KAAK;MACrD,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7BE,kBAAkB,CAACF,KAAK,EAAE,KAAK,CAAC;MAClC;IACF,CAAC,CAAC;EACJ;AACF", "ignoreList": []}