{"version": 3, "names": ["React", "useKeyedChildListeners", "current", "keyedListeners", "useRef", "Object", "assign", "create", "getState", "beforeRemove", "addKeyedListener", "useCallback", "type", "key", "listener", "undefined"], "sourceRoot": "../../src", "sources": ["useKeyedChildListeners.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAI9B;AACA;AACA;AACA,OAAO,SAASC,sBAAsBA,CAAA,EAAG;EACvC,MAAM;IAAEC,OAAO,EAAEC;EAAe,CAAC,GAAGH,KAAK,CAACI,MAAM,CAM9CC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACE,MAAM,CAAC,IAAI,CAAC,EAAE;IACjCC,QAAQ,EAAE,CAAC,CAAC;IACZC,YAAY,EAAE,CAAC;EACjB,CAAC,CACH,CAAC;EAED,MAAMC,gBAAgB,GAAGV,KAAK,CAACW,WAAW,CACxC,CACEC,IAAO,EACPC,GAAW,EACXC,QAA6B,KAC1B;IACH;IACAX,cAAc,CAACS,IAAI,CAAC,CAACC,GAAG,CAAC,GAAGC,QAAQ;IAEpC,OAAO,MAAM;MACX;MACAX,cAAc,CAACS,IAAI,CAAC,CAACC,GAAG,CAAC,GAAGE,SAAS;IACvC,CAAC;EACH,CAAC,EACD,CAACZ,cAAc,CACjB,CAAC;EAED,OAAO;IACLA,cAAc;IACdO;EACF,CAAC;AACH", "ignoreList": []}