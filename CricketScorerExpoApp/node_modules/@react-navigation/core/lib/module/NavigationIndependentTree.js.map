{"version": 3, "names": ["React", "NavigationContext", "NavigationIndependentTreeContext", "NavigationRouteContext", "jsx", "_jsx", "NavigationIndependentTree", "children", "Provider", "value", "undefined"], "sourceRoot": "../../src", "sources": ["NavigationIndependentTree.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,iBAAiB,QAAQ,wBAAqB;AACvD,SAASC,gCAAgC,QAAQ,uCAAoC;AACrF,SAASC,sBAAsB,QAAQ,6BAA0B;;AAEjE;AACA;AACA;AAFA,SAAAC,GAAA,IAAAC,IAAA;AAGA,OAAO,SAASC,yBAAyBA,CAAC;EACxCC;AAGF,CAAC,EAAE;EACD;IAAA;IACE;IACAF,IAAA,CAACF,sBAAsB,CAACK,QAAQ;MAACC,KAAK,EAAEC,SAAU;MAAAH,QAAA,eAChDF,IAAA,CAACJ,iBAAiB,CAACO,QAAQ;QAACC,KAAK,EAAEC,SAAU;QAAAH,QAAA,eAC3CF,IAAA,CAACH,gCAAgC,CAACM,QAAQ;UAACC,KAAK;UAAAF,QAAA,EAC7CA;QAAQ,CACgC;MAAC,CAClB;IAAC,CACE;EAAC;AAEtC", "ignoreList": []}