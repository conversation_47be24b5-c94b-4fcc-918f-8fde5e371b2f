{"version": 3, "names": ["CommonActions", "BaseRouter", "DrawerActions", "DrawerRouter", "StackActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TabActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": "../../src", "sources": ["index.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,aAAa,MAAM,oBAAiB;AAEhD,SAASA,aAAa;AAEtB,SAASC,UAAU,QAAQ,iBAAc;AAQzC,SAASC,aAAa,EAAEC,YAAY,QAAQ,mBAAgB;AAO5D,SAASC,YAAY,EAAEC,WAAW,QAAQ,kBAAe;AAOzD,SAASC,UAAU,EAAEC,SAAS,QAAQ,gBAAa;AACnD,cAAc,YAAS", "ignoreList": []}