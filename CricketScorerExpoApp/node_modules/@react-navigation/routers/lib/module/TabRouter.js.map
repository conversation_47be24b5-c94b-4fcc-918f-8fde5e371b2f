{"version": 3, "names": ["nanoid", "BaseRouter", "TYPE_ROUTE", "TabActions", "jumpTo", "name", "params", "type", "payload", "getRouteHistory", "routes", "index", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initialRouteName", "history", "key", "initialRouteIndex", "i", "unshift", "findIndex", "route", "changeIndex", "state", "currentRouteKey", "filter", "it", "lastHistoryRouteItemIndex", "findLastIndex", "item", "slice", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "router", "getInitialState", "routeNames", "routeParamList", "undefined", "includes", "indexOf", "map", "stale", "preloadedRouteKeys", "getRehydratedState", "partialState", "find", "r", "Math", "min", "max", "length", "routeKeys", "getStateForRouteNamesChange", "routeKeyChanges", "getStateForRouteFocus", "getStateForAction", "action", "routeGetIdList", "updatedState", "getId", "currentId", "nextId", "merge", "path", "previousKey", "routeIndex", "newRoute", "record", "actionCreators"], "sourceRoot": "../../src", "sources": ["TabRouter.tsx"], "mappings": ";;AAAA,SAASA,MAAM,QAAQ,mBAAmB;AAE1C,SAASC,UAAU,QAAQ,iBAAc;AAyEzC,MAAMC,UAAU,GAAG,OAAgB;AAEnC,OAAO,MAAMC,UAAU,GAAG;EACxBC,MAAMA,CAACC,IAAY,EAAEC,MAAe,EAAiB;IACnD,OAAO;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE;QAAEH,IAAI;QAAEC;MAAO;IAAE,CAAC;EACvD;AACF,CAAC;AAED,MAAMG,eAAe,GAAGA,CACtBC,MAAuB,EACvBC,KAAa,EACbC,YAA0B,EAC1BC,gBAAoC,KACjC;EACH,MAAMC,OAAO,GAAG,CAAC;IAAEP,IAAI,EAAEL,UAAU;IAAEa,GAAG,EAAEL,MAAM,CAACC,KAAK,CAAC,CAACI;EAAI,CAAC,CAAC;EAC9D,IAAIC,iBAAiB;EAErB,QAAQJ,YAAY;IAClB,KAAK,OAAO;MACV,KAAK,IAAIK,CAAC,GAAGN,KAAK,EAAEM,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC9BH,OAAO,CAACI,OAAO,CAAC;UAAEX,IAAI,EAAEL,UAAU;UAAEa,GAAG,EAAEL,MAAM,CAACO,CAAC,GAAG,CAAC,CAAC,CAACF;QAAI,CAAC,CAAC;MAC/D;MACA;IACF,KAAK,YAAY;MACf,IAAIJ,KAAK,KAAK,CAAC,EAAE;QACfG,OAAO,CAACI,OAAO,CAAC;UACdX,IAAI,EAAEL,UAAU;UAChBa,GAAG,EAAEL,MAAM,CAAC,CAAC,CAAC,CAACK;QACjB,CAAC,CAAC;MACJ;MACA;IACF,KAAK,cAAc;MACjBC,iBAAiB,GAAGN,MAAM,CAACS,SAAS,CACjCC,KAAK,IAAKA,KAAK,CAACf,IAAI,KAAKQ,gBAC5B,CAAC;MACDG,iBAAiB,GAAGA,iBAAiB,KAAK,CAAC,CAAC,GAAG,CAAC,GAAGA,iBAAiB;MAEpE,IAAIL,KAAK,KAAKK,iBAAiB,EAAE;QAC/BF,OAAO,CAACI,OAAO,CAAC;UACdX,IAAI,EAAEL,UAAU;UAChBa,GAAG,EAAEL,MAAM,CAACM,iBAAiB,CAAC,CAACD;QACjC,CAAC,CAAC;MACJ;MACA;IACF,KAAK,SAAS;IACd,KAAK,aAAa;MAChB;MACA;EACJ;EAEA,OAAOD,OAAO;AAChB,CAAC;AAED,MAAMO,WAAW,GAAGA,CAClBC,KAAwC,EACxCX,KAAa,EACbC,YAA0B,EAC1BC,gBAAoC,KACjC;EACH,IAAIC,OAAO,GAAGQ,KAAK,CAACR,OAAO;EAE3B,IAAIF,YAAY,KAAK,SAAS,IAAIA,YAAY,KAAK,aAAa,EAAE;IAChE,MAAMW,eAAe,GAAGD,KAAK,CAACZ,MAAM,CAACC,KAAK,CAAC,CAACI,GAAG;IAE/C,IAAIH,YAAY,KAAK,SAAS,EAAE;MAC9B;MACAE,OAAO,GAAGA,OAAO,CAACU,MAAM,CAAEC,EAAE,IAC1BA,EAAE,CAAClB,IAAI,KAAK,OAAO,GAAGkB,EAAE,CAACV,GAAG,KAAKQ,eAAe,GAAG,KACrD,CAAC;IACH,CAAC,MAAM,IAAIX,YAAY,KAAK,aAAa,EAAE;MACzC,MAAMc,yBAAyB,GAAGZ,OAAO,CAACa,aAAa,CACpDC,IAAI,IAAKA,IAAI,CAACrB,IAAI,KAAK,OAC1B,CAAC;MAED,IAAIgB,eAAe,KAAKT,OAAO,CAACY,yBAAyB,CAAC,EAAEX,GAAG,EAAE;QAC/D;QACA;QACA;QACAD,OAAO,GAAG,CACR,GAAGA,OAAO,CAACe,KAAK,CAAC,CAAC,EAAEH,yBAAyB,CAAC,EAC9C,GAAGZ,OAAO,CAACe,KAAK,CAACH,yBAAyB,GAAG,CAAC,CAAC,CAChD;MACH;IACF;IAEAZ,OAAO,GAAGA,OAAO,CAACgB,MAAM,CAAC;MACvBvB,IAAI,EAAEL,UAAU;MAChBa,GAAG,EAAEQ;IACP,CAAC,CAAC;EACJ,CAAC,MAAM;IACLT,OAAO,GAAGL,eAAe,CACvBa,KAAK,CAACZ,MAAM,EACZC,KAAK,EACLC,YAAY,EACZC,gBACF,CAAC;EACH;EAEA,OAAO;IACL,GAAGS,KAAK;IACRX,KAAK;IACLG;EACF,CAAC;AACH,CAAC;AAED,OAAO,SAASiB,SAASA,CAAC;EACxBlB,gBAAgB;EAChBD,YAAY,GAAG;AACC,CAAC,EAAE;EACnB,MAAMoB,MAGL,GAAG;IACF,GAAG/B,UAAU;IAEbM,IAAI,EAAE,KAAK;IAEX0B,eAAeA,CAAC;MAAEC,UAAU;MAAEC;IAAe,CAAC,EAAE;MAC9C,MAAMxB,KAAK,GACTE,gBAAgB,KAAKuB,SAAS,IAAIF,UAAU,CAACG,QAAQ,CAACxB,gBAAgB,CAAC,GACnEqB,UAAU,CAACI,OAAO,CAACzB,gBAAgB,CAAC,GACpC,CAAC;MAEP,MAAMH,MAAM,GAAGwB,UAAU,CAACK,GAAG,CAAElC,IAAI,KAAM;QACvCA,IAAI;QACJU,GAAG,EAAE,GAAGV,IAAI,IAAIL,MAAM,CAAC,CAAC,EAAE;QAC1BM,MAAM,EAAE6B,cAAc,CAAC9B,IAAI;MAC7B,CAAC,CAAC,CAAC;MAEH,MAAMS,OAAO,GAAGL,eAAe,CAC7BC,MAAM,EACNC,KAAK,EACLC,YAAY,EACZC,gBACF,CAAC;MAED,OAAO;QACL2B,KAAK,EAAE,KAAK;QACZjC,IAAI,EAAE,KAAK;QACXQ,GAAG,EAAE,OAAOf,MAAM,CAAC,CAAC,EAAE;QACtBW,KAAK;QACLuB,UAAU;QACVpB,OAAO;QACPJ,MAAM;QACN+B,kBAAkB,EAAE;MACtB,CAAC;IACH,CAAC;IAEDC,kBAAkBA,CAACC,YAAY,EAAE;MAAET,UAAU;MAAEC;IAAe,CAAC,EAAE;MAC/D,MAAMb,KAAK,GAAGqB,YAAY;MAE1B,IAAIrB,KAAK,CAACkB,KAAK,KAAK,KAAK,EAAE;QACzB,OAAOlB,KAAK;MACd;MAEA,MAAMZ,MAAM,GAAGwB,UAAU,CAACK,GAAG,CAAElC,IAAI,IAAK;QACtC,MAAMe,KAAK,GACTE,KAAK,CACLZ,MAAM,CAACkC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACxC,IAAI,KAAKA,IAAI,CAAC;QAErC,OAAO;UACL,GAAGe,KAAK;UACRf,IAAI;UACJU,GAAG,EACDK,KAAK,IAAIA,KAAK,CAACf,IAAI,KAAKA,IAAI,IAAIe,KAAK,CAACL,GAAG,GACrCK,KAAK,CAACL,GAAG,GACT,GAAGV,IAAI,IAAIL,MAAM,CAAC,CAAC,EAAE;UAC3BM,MAAM,EACJ6B,cAAc,CAAC9B,IAAI,CAAC,KAAK+B,SAAS,GAC9B;YACE,GAAGD,cAAc,CAAC9B,IAAI,CAAC;YACvB,IAAIe,KAAK,GAAGA,KAAK,CAACd,MAAM,GAAG8B,SAAS;UACtC,CAAC,GACDhB,KAAK,GACHA,KAAK,CAACd,MAAM,GACZ8B;QACV,CAAC;MACH,CAAC,CAAC;MAEF,MAAMzB,KAAK,GAAGmC,IAAI,CAACC,GAAG,CACpBD,IAAI,CAACE,GAAG,CAACd,UAAU,CAACI,OAAO,CAAChB,KAAK,CAACZ,MAAM,CAACY,KAAK,EAAEX,KAAK,IAAI,CAAC,CAAC,EAAEN,IAAI,CAAC,EAAE,CAAC,CAAC,EACtEK,MAAM,CAACuC,MAAM,GAAG,CAClB,CAAC;MAED,MAAMC,SAAS,GAAGxC,MAAM,CAAC6B,GAAG,CAAEnB,KAAK,IAAKA,KAAK,CAACL,GAAG,CAAC;MAElD,MAAMD,OAAO,GACXQ,KAAK,CAACR,OAAO,EAAEU,MAAM,CAAEC,EAAE,IAAKyB,SAAS,CAACb,QAAQ,CAACZ,EAAE,CAACV,GAAG,CAAC,CAAC,IAAI,EAAE;MAEjE,OAAOM,WAAW,CAChB;QACEmB,KAAK,EAAE,KAAK;QACZjC,IAAI,EAAE,KAAK;QACXQ,GAAG,EAAE,OAAOf,MAAM,CAAC,CAAC,EAAE;QACtBW,KAAK;QACLuB,UAAU;QACVpB,OAAO;QACPJ,MAAM;QACN+B,kBAAkB,EAChBnB,KAAK,CAACmB,kBAAkB,EAAEjB,MAAM,CAAET,GAAG,IACnCmC,SAAS,CAACb,QAAQ,CAACtB,GAAG,CACxB,CAAC,IAAI;MACT,CAAC,EACDJ,KAAK,EACLC,YAAY,EACZC,gBACF,CAAC;IACH,CAAC;IAEDsC,2BAA2BA,CACzB7B,KAAK,EACL;MAAEY,UAAU;MAAEC,cAAc;MAAEiB;IAAgB,CAAC,EAC/C;MACA,MAAM1C,MAAM,GAAGwB,UAAU,CAACK,GAAG,CAC1BlC,IAAI,IACHiB,KAAK,CAACZ,MAAM,CAACkC,IAAI,CACdC,CAAC,IAAKA,CAAC,CAACxC,IAAI,KAAKA,IAAI,IAAI,CAAC+C,eAAe,CAACf,QAAQ,CAACQ,CAAC,CAACxC,IAAI,CAC5D,CAAC,IAAI;QACHA,IAAI;QACJU,GAAG,EAAE,GAAGV,IAAI,IAAIL,MAAM,CAAC,CAAC,EAAE;QAC1BM,MAAM,EAAE6B,cAAc,CAAC9B,IAAI;MAC7B,CACJ,CAAC;MAED,MAAMM,KAAK,GAAGmC,IAAI,CAACE,GAAG,CACpB,CAAC,EACDd,UAAU,CAACI,OAAO,CAAChB,KAAK,CAACZ,MAAM,CAACY,KAAK,CAACX,KAAK,CAAC,CAACN,IAAI,CACnD,CAAC;MAED,IAAIS,OAAO,GAAGQ,KAAK,CAACR,OAAO,CAACU,MAAM;MAChC;MACCC,EAAE,IAAKA,EAAE,CAAClB,IAAI,KAAK,OAAO,IAAIG,MAAM,CAACkC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC9B,GAAG,KAAKU,EAAE,CAACV,GAAG,CACpE,CAAC;MAED,IAAI,CAACD,OAAO,CAACmC,MAAM,EAAE;QACnBnC,OAAO,GAAGL,eAAe,CACvBC,MAAM,EACNC,KAAK,EACLC,YAAY,EACZC,gBACF,CAAC;MACH;MAEA,OAAO;QACL,GAAGS,KAAK;QACRR,OAAO;QACPoB,UAAU;QACVxB,MAAM;QACNC;MACF,CAAC;IACH,CAAC;IAED0C,qBAAqBA,CAAC/B,KAAK,EAAEP,GAAG,EAAE;MAChC,MAAMJ,KAAK,GAAGW,KAAK,CAACZ,MAAM,CAACS,SAAS,CAAE0B,CAAC,IAAKA,CAAC,CAAC9B,GAAG,KAAKA,GAAG,CAAC;MAE1D,IAAIJ,KAAK,KAAK,CAAC,CAAC,IAAIA,KAAK,KAAKW,KAAK,CAACX,KAAK,EAAE;QACzC,OAAOW,KAAK;MACd;MAEA,OAAOD,WAAW,CAACC,KAAK,EAAEX,KAAK,EAAEC,YAAY,EAAEC,gBAAgB,CAAC;IAClE,CAAC;IAEDyC,iBAAiBA,CAAChC,KAAK,EAAEiC,MAAM,EAAE;MAAEpB,cAAc;MAAEqB;IAAe,CAAC,EAAE;MACnE,QAAQD,MAAM,CAAChD,IAAI;QACjB,KAAK,SAAS;QACd,KAAK,UAAU;QACf,KAAK,qBAAqB;UAAE;YAC1B,MAAMI,KAAK,GAAGW,KAAK,CAACZ,MAAM,CAACS,SAAS,CACjCC,KAAK,IAAKA,KAAK,CAACf,IAAI,KAAKkD,MAAM,CAAC/C,OAAO,CAACH,IAC3C,CAAC;YAED,IAAIM,KAAK,KAAK,CAAC,CAAC,EAAE;cAChB,OAAO,IAAI;YACb;YAEA,MAAM8C,YAAY,GAAGpC,WAAW,CAC9B;cACE,GAAGC,KAAK;cACRZ,MAAM,EAAEY,KAAK,CAACZ,MAAM,CAAC6B,GAAG,CAAEnB,KAAK,IAAK;gBAClC,IAAIA,KAAK,CAACf,IAAI,KAAKkD,MAAM,CAAC/C,OAAO,CAACH,IAAI,EAAE;kBACtC,OAAOe,KAAK;gBACd;gBAEA,MAAMsC,KAAK,GAAGF,cAAc,CAACpC,KAAK,CAACf,IAAI,CAAC;gBAExC,MAAMsD,SAAS,GAAGD,KAAK,GAAG;kBAAEpD,MAAM,EAAEc,KAAK,CAACd;gBAAO,CAAC,CAAC;gBACnD,MAAMsD,MAAM,GAAGF,KAAK,GAAG;kBAAEpD,MAAM,EAAEiD,MAAM,CAAC/C,OAAO,CAACF;gBAAO,CAAC,CAAC;gBAEzD,MAAMS,GAAG,GACP4C,SAAS,KAAKC,MAAM,GAChBxC,KAAK,CAACL,GAAG,GACT,GAAGK,KAAK,CAACf,IAAI,IAAIL,MAAM,CAAC,CAAC,EAAE;gBAEjC,IAAIM,MAAM;gBAEV,IACE,CAACiD,MAAM,CAAChD,IAAI,KAAK,UAAU,IACzBgD,MAAM,CAAChD,IAAI,KAAK,qBAAqB,KACvCgD,MAAM,CAAC/C,OAAO,CAACqD,KAAK,IACpBF,SAAS,KAAKC,MAAM,EACpB;kBACAtD,MAAM,GACJiD,MAAM,CAAC/C,OAAO,CAACF,MAAM,KAAK8B,SAAS,IACnCD,cAAc,CAACf,KAAK,CAACf,IAAI,CAAC,KAAK+B,SAAS,GACpC;oBACE,GAAGD,cAAc,CAACf,KAAK,CAACf,IAAI,CAAC;oBAC7B,GAAGe,KAAK,CAACd,MAAM;oBACf,GAAGiD,MAAM,CAAC/C,OAAO,CAACF;kBACpB,CAAC,GACDc,KAAK,CAACd,MAAM;gBACpB,CAAC,MAAM;kBACLA,MAAM,GACJ6B,cAAc,CAACf,KAAK,CAACf,IAAI,CAAC,KAAK+B,SAAS,GACpC;oBACE,GAAGD,cAAc,CAACf,KAAK,CAACf,IAAI,CAAC;oBAC7B,GAAGkD,MAAM,CAAC/C,OAAO,CAACF;kBACpB,CAAC,GACDiD,MAAM,CAAC/C,OAAO,CAACF,MAAM;gBAC7B;gBAEA,MAAMwD,IAAI,GACRP,MAAM,CAAChD,IAAI,KAAK,UAAU,IAAIgD,MAAM,CAAC/C,OAAO,CAACsD,IAAI,IAAI,IAAI,GACrDP,MAAM,CAAC/C,OAAO,CAACsD,IAAI,GACnB1C,KAAK,CAAC0C,IAAI;gBAEhB,OAAOxD,MAAM,KAAKc,KAAK,CAACd,MAAM,IAAIwD,IAAI,KAAK1C,KAAK,CAAC0C,IAAI,GACjD;kBAAE,GAAG1C,KAAK;kBAAEL,GAAG;kBAAE+C,IAAI;kBAAExD;gBAAO,CAAC,GAC/Bc,KAAK;cACX,CAAC;YACH,CAAC,EACDT,KAAK,EACLC,YAAY,EACZC,gBACF,CAAC;YAED,OAAO;cACL,GAAG4C,YAAY;cACfhB,kBAAkB,EAAEgB,YAAY,CAAChB,kBAAkB,CAACjB,MAAM,CACvDT,GAAG,IAAKA,GAAG,KAAKO,KAAK,CAACZ,MAAM,CAAC+C,YAAY,CAAC9C,KAAK,CAAC,CAACI,GACpD;YACF,CAAC;UACH;QAEA,KAAK,SAAS;UAAE;YACd,IAAIO,KAAK,CAACR,OAAO,CAACmC,MAAM,KAAK,CAAC,EAAE;cAC9B,OAAO,IAAI;YACb;YAEA,MAAMc,WAAW,GAAGzC,KAAK,CAACR,OAAO,CAACQ,KAAK,CAACR,OAAO,CAACmC,MAAM,GAAG,CAAC,CAAC,EAAElC,GAAG;YAChE,MAAMJ,KAAK,GAAGW,KAAK,CAACZ,MAAM,CAACiB,aAAa,CACrCP,KAAK,IAAKA,KAAK,CAACL,GAAG,KAAKgD,WAC3B,CAAC;YAED,IAAIpD,KAAK,KAAK,CAAC,CAAC,EAAE;cAChB,OAAO,IAAI;YACb;YAEA,OAAO;cACL,GAAGW,KAAK;cACRmB,kBAAkB,EAAEnB,KAAK,CAACmB,kBAAkB,CAACjB,MAAM,CAChDT,GAAG,IAAKA,GAAG,KAAKO,KAAK,CAACZ,MAAM,CAACC,KAAK,CAAC,CAACI,GACvC,CAAC;cACDD,OAAO,EAAEQ,KAAK,CAACR,OAAO,CAACe,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cACnClB;YACF,CAAC;UACH;QAEA,KAAK,SAAS;UAAE;YACd,MAAMqD,UAAU,GAAG1C,KAAK,CAACZ,MAAM,CAACS,SAAS,CACtCC,KAAK,IAAKA,KAAK,CAACf,IAAI,KAAKkD,MAAM,CAAC/C,OAAO,CAACH,IAC3C,CAAC;YAED,IAAI2D,UAAU,KAAK,CAAC,CAAC,EAAE;cACrB,OAAO,IAAI;YACb;YAEA,MAAM5C,KAAK,GAAGE,KAAK,CAACZ,MAAM,CAACsD,UAAU,CAAC;YAEtC,MAAMN,KAAK,GAAGF,cAAc,CAACpC,KAAK,CAACf,IAAI,CAAC;YAExC,MAAMsD,SAAS,GAAGD,KAAK,GAAG;cAAEpD,MAAM,EAAEc,KAAK,CAACd;YAAO,CAAC,CAAC;YACnD,MAAMsD,MAAM,GAAGF,KAAK,GAAG;cAAEpD,MAAM,EAAEiD,MAAM,CAAC/C,OAAO,CAACF;YAAO,CAAC,CAAC;YAEzD,MAAMS,GAAG,GACP4C,SAAS,KAAKC,MAAM,GAAGxC,KAAK,CAACL,GAAG,GAAG,GAAGK,KAAK,CAACf,IAAI,IAAIL,MAAM,CAAC,CAAC,EAAE;YAEhE,MAAMM,MAAM,GACViD,MAAM,CAAC/C,OAAO,CAACF,MAAM,KAAK8B,SAAS,IACnCD,cAAc,CAACf,KAAK,CAACf,IAAI,CAAC,KAAK+B,SAAS,GACpC;cACE,GAAGD,cAAc,CAACf,KAAK,CAACf,IAAI,CAAC;cAC7B,GAAGkD,MAAM,CAAC/C,OAAO,CAACF;YACpB,CAAC,GACD8B,SAAS;YAEf,MAAM6B,QAAQ,GACZ3D,MAAM,KAAKc,KAAK,CAACd,MAAM,GAAG;cAAE,GAAGc,KAAK;cAAEL,GAAG;cAAET;YAAO,CAAC,GAAGc,KAAK;YAE7D,OAAO;cACL,GAAGE,KAAK;cACRmB,kBAAkB,EAAEnB,KAAK,CAACmB,kBAAkB,CACzCjB,MAAM,CAAET,GAAG,IAAKA,GAAG,KAAKK,KAAK,CAACL,GAAG,CAAC,CAClCe,MAAM,CAACmC,QAAQ,CAAClD,GAAG,CAAC;cACvBL,MAAM,EAAEY,KAAK,CAACZ,MAAM,CAAC6B,GAAG,CAAC,CAACnB,KAAK,EAAET,KAAK,KACpCA,KAAK,KAAKqD,UAAU,GAAGC,QAAQ,GAAG7C,KACpC,CAAC;cACDN,OAAO,EACLC,GAAG,KAAKK,KAAK,CAACL,GAAG,GACbO,KAAK,CAACR,OAAO,GACbQ,KAAK,CAACR,OAAO,CAACU,MAAM,CAAE0C,MAAM,IAAKA,MAAM,CAACnD,GAAG,KAAKK,KAAK,CAACL,GAAG;YACjE,CAAC;UACH;QAEA;UACE,OAAOd,UAAU,CAACqD,iBAAiB,CAAChC,KAAK,EAAEiC,MAAM,CAAC;MACtD;IACF,CAAC;IAEDY,cAAc,EAAEhE;EAClB,CAAC;EAED,OAAO6B,MAAM;AACf", "ignoreList": []}