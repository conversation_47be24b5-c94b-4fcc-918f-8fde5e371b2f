{"version": 3, "names": ["nanoid", "BaseRouter", "getStateForAction", "state", "action", "type", "index", "source", "routes", "findIndex", "r", "key", "map", "i", "params", "payload", "nextState", "length", "some", "route", "routeNames", "includes", "name", "stale", "shouldActionChangeFocus"], "sourceRoot": "../../src", "sources": ["BaseRouter.tsx"], "mappings": ";;AAAA,SAASA,MAAM,QAAQ,mBAAmB;AAQ1C;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,iBAAiBA,CACfC,KAAY,EACZC,MAA8B,EACM;IACpC,QAAQA,MAAM,CAACC,IAAI;MACjB,KAAK,YAAY;MACjB,KAAK,gBAAgB;QAAE;UACrB,MAAMC,KAAK,GAAGF,MAAM,CAACG,MAAM,GACvBJ,KAAK,CAACK,MAAM,CAACC,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAACC,GAAG,KAAKP,MAAM,CAACG,MAAM,CAAC,GACtDJ,KAAK,CAACG,KAAK;UAEf,IAAIA,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,OAAO,IAAI;UACb;UAEA,OAAO;YACL,GAAGH,KAAK;YACRK,MAAM,EAAEL,KAAK,CAACK,MAAM,CAACI,GAAG,CAAC,CAACF,CAAC,EAAEG,CAAC,KAC5BA,CAAC,KAAKP,KAAK,GACP;cACE,GAAGI,CAAC;cACJI,MAAM,EACJV,MAAM,CAACC,IAAI,KAAK,gBAAgB,GAC5BD,MAAM,CAACW,OAAO,CAACD,MAAM,GACrB;gBAAE,GAAGJ,CAAC,CAACI,MAAM;gBAAE,GAAGV,MAAM,CAACW,OAAO,CAACD;cAAO;YAChD,CAAC,GACDJ,CACN;UACF,CAAC;QACH;MAEA,KAAK,OAAO;QAAE;UACZ,MAAMM,SAAS,GAAGZ,MAAM,CAACW,OAAsC;UAE/D,IACEC,SAAS,CAACR,MAAM,CAACS,MAAM,KAAK,CAAC,IAC7BD,SAAS,CAACR,MAAM,CAACU,IAAI,CAClBC,KAAuB,IAAK,CAAChB,KAAK,CAACiB,UAAU,CAACC,QAAQ,CAACF,KAAK,CAACG,IAAI,CACpE,CAAC,EACD;YACA,OAAO,IAAI;UACb;UAEA,IAAIN,SAAS,CAACO,KAAK,KAAK,KAAK,EAAE;YAC7B,IACEpB,KAAK,CAACiB,UAAU,CAACH,MAAM,KAAKD,SAAS,CAACI,UAAU,CAACH,MAAM,IACvDD,SAAS,CAACI,UAAU,CAACF,IAAI,CACtBI,IAAI,IAAK,CAACnB,KAAK,CAACiB,UAAU,CAACC,QAAQ,CAACC,IAAI,CAC3C,CAAC,EACD;cACA,OAAO,IAAI;YACb;YAEA,OAAO;cACL,GAAGN,SAAS;cACZR,MAAM,EAAEQ,SAAS,CAACR,MAAM,CAACI,GAAG,CAAEO,KAAK,IACjCA,KAAK,CAACR,GAAG,GAAGQ,KAAK,GAAG;gBAAE,GAAGA,KAAK;gBAAER,GAAG,EAAE,GAAGQ,KAAK,CAACG,IAAI,IAAItB,MAAM,CAAC,CAAC;cAAG,CACnE;YACF,CAAC;UACH;UAEA,OAAOgB,SAAS;QAClB;MAEA;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAEDQ,uBAAuBA,CAACpB,MAA8B,EAAE;IACtD,OAAOA,MAAM,CAACC,IAAI,KAAK,UAAU,IAAID,MAAM,CAACC,IAAI,KAAK,qBAAqB;EAC5E;AACF,CAAC", "ignoreList": []}