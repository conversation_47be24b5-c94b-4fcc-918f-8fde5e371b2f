{"version": 3, "file": "StackRouter.d.ts", "sourceRoot": "", "sources": ["../../../src/StackRouter.tsx"], "names": [], "mappings": "AAGA,OAAO,KAAK,EAEV,oBAAoB,EACpB,eAAe,EACf,eAAe,EACf,aAAa,EAEb,MAAM,EACP,MAAM,SAAS,CAAC;AAEjB,MAAM,MAAM,eAAe,GACvB;IACE,IAAI,EAAE,SAAS,CAAC;IAChB,OAAO,EAAE;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,MAAM,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC;IAC3C,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB,GACD;IACE,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EAAE;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,MAAM,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC;IAC3C,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB,GACD;IACE,IAAI,EAAE,KAAK,CAAC;IACZ,OAAO,EAAE;QAAE,KAAK,EAAE,MAAM,CAAA;KAAE,CAAC;IAC3B,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB,GACD;IACE,IAAI,EAAE,YAAY,CAAC;IACnB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB,GACD;IACE,IAAI,EAAE,QAAQ,CAAC;IACf,OAAO,EAAE;QACP,IAAI,EAAE,MAAM,CAAC;QACb,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,KAAK,CAAC,EAAE,OAAO,CAAC;KACjB,CAAC;IACF,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB,CAAC;AAEN,MAAM,MAAM,kBAAkB,GAAG,oBAAoB,CAAC;AAEtD,MAAM,MAAM,oBAAoB,CAAC,SAAS,SAAS,aAAa,IAC9D,eAAe,CAAC,SAAS,CAAC,GAAG;IAC3B;;OAEG;IACH,IAAI,EAAE,OAAO,CAAC;IACd;;OAEG;IACH,eAAe,EAAE,eAAe,CAAC,SAAS,EAAE,MAAM,SAAS,CAAC,EAAE,CAAC;CAChE,CAAC;AAEJ,MAAM,MAAM,kBAAkB,CAAC,SAAS,SAAS,aAAa,IAAI;IAChE;;;;;OAKG;IACH,OAAO,CAAC,SAAS,SAAS,MAAM,SAAS,EACvC,GAAG,IAAI,EAAE,SAAS,SAAS,OAAO,GAC9B,SAAS,SAAS,SAAS,CAAC,SAAS,CAAC,GACpC,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,GAClD,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,GACnD,KAAK,GACR,IAAI,CAAC;IAER;;;;;OAKG;IACH,IAAI,CAAC,SAAS,SAAS,MAAM,SAAS,EACpC,GAAG,IAAI,EAAE,SAAS,SAAS,OAAO,GAC9B,SAAS,SAAS,SAAS,CAAC,SAAS,CAAC,GACpC,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,GAClD,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,GACnD,KAAK,GACR,IAAI,CAAC;IAER;;OAEG;IACH,GAAG,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE1B;;OAEG;IACH,QAAQ,IAAI,IAAI,CAAC;IAEjB;;;;;;;OAOG;IACH,KAAK,CAAC,SAAS,SAAS,MAAM,SAAS,EACrC,GAAG,IAAI,EAAE,SAAS,SAAS,OAAO,GAC9B,SAAS,SAAS,SAAS,CAAC,SAAS,CAAC,GACpC;QACE,MAAM,EAAE,SAAS;QACjB,MAAM,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC;QAC7B,OAAO,CAAC,EAAE;YAAE,KAAK,CAAC,EAAE,OAAO,CAAA;SAAE;KAC9B,GACD;QACE,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,SAAS,CAAC,SAAS,CAAC;QAC5B,OAAO,CAAC,EAAE;YAAE,KAAK,CAAC,EAAE,OAAO,CAAA;SAAE;KAC9B,GACH,KAAK,GACR,IAAI,CAAC;CACT,CAAC;AAEF,eAAO,MAAM,YAAY;kBACT,MAAM,WAAW,MAAM,GAAG,eAAe;eAG5C,MAAM,WAAW,MAAM,GAAG,eAAe;gBAGzC,MAAM,GAAO,eAAe;gBAG3B,eAAe;gBAInB,MAAM,WACH,MAAM,YACL;QAAE,KAAK,CAAC,EAAE,OAAO,CAAA;KAAE,GAC5B,eAAe;CAgBnB,CAAC;AAEF,wBAAgB,WAAW,CAAC,OAAO,EAAE,kBAAkB,mGAqlBtD"}