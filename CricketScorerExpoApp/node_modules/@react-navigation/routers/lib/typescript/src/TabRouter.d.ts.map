{"version": 3, "file": "TabRouter.d.ts", "sourceRoot": "", "sources": ["../../../src/TabRouter.tsx"], "names": [], "mappings": "AAGA,OAAO,KAAK,EAEV,oBAAoB,EACpB,eAAe,EACf,aAAa,EAGb,MAAM,EACP,MAAM,SAAS,CAAC;AAEjB,MAAM,MAAM,aAAa,GAAG;IAC1B,IAAI,EAAE,SAAS,CAAC;IAChB,OAAO,EAAE;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,MAAM,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC;IAC3C,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB,CAAC;AAEF,MAAM,MAAM,YAAY,GACpB,YAAY,GACZ,cAAc,GACd,OAAO,GACP,SAAS,GACT,aAAa,GACb,MAAM,CAAC;AAEX,MAAM,MAAM,gBAAgB,GAAG,oBAAoB,GAAG;IACpD;;;;;;;;OAQG;IACH,YAAY,CAAC,EAAE,YAAY,CAAC;CAC7B,CAAC;AAEF,MAAM,MAAM,kBAAkB,CAAC,SAAS,SAAS,aAAa,IAAI,IAAI,CACpE,eAAe,CAAC,SAAS,CAAC,EAC1B,SAAS,CACV,GAAG;IACF;;OAEG;IACH,IAAI,EAAE,KAAK,CAAC;IACZ;;OAEG;IACH,OAAO,EAAE;QAAE,IAAI,EAAE,OAAO,CAAC;QAAC,GAAG,EAAE,MAAM,CAAA;KAAE,EAAE,CAAC;IAC1C;;OAEG;IACH,kBAAkB,EAAE,MAAM,EAAE,CAAC;CAC9B,CAAC;AAEF,MAAM,MAAM,gBAAgB,CAAC,SAAS,SAAS,aAAa,IAAI;IAC9D;;;;;OAKG;IACH,MAAM,CAAC,SAAS,SAAS,MAAM,SAAS,EACtC,GAAG,IAAI,EAAE,SAAS,SAAS,OAAO,GAC9B,SAAS,SAAS,SAAS,CAAC,SAAS,CAAC,GACpC,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,GAClD,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,GACnD,KAAK,GACR,IAAI,CAAC;CACT,CAAC;AAIF,eAAO,MAAM,UAAU;iBACR,MAAM,WAAW,MAAM,GAAG,aAAa;CAGrD,CAAC;AAmGF,wBAAgB,SAAS,CAAC,EACxB,gBAAgB,EAChB,YAA2B,GAC5B,EAAE,gBAAgB,+FA0TlB"}