{"name": "@react-navigation/routers", "description": "Routers to help build custom navigators", "version": "7.4.0", "keywords": ["react", "react-native", "react-navigation"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/routers"}, "bugs": {"url": "https://github.com/react-navigation/react-navigation/issues"}, "homepage": "https://reactnavigation.org/docs/custom-routers/", "source": "./src/index.tsx", "main": "./lib/module/index.js", "types": "./lib/typescript/src/index.d.ts", "exports": {".": {"types": "./lib/typescript/src/index.d.ts", "default": "./lib/module/index.js"}, "./package.json": "./package.json"}, "files": ["src", "lib", "!**/__tests__"], "sideEffects": false, "publishConfig": {"access": "public"}, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"nanoid": "^3.3.11"}, "devDependencies": {"@jest/globals": "^29.7.0", "del-cli": "^6.0.0", "react-native-builder-bob": "^0.40.9", "typescript": "^5.8.3"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json"}]]}, "gitHead": "52e5ec64745b399655adf7bd9773e31372d51e62"}