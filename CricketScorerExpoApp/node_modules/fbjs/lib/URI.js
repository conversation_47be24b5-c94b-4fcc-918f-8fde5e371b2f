/**
 * Copyright (c) 2013-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * 
 */
'use strict';

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

var URI = /*#__PURE__*/function () {
  function URI(uri) {
    _defineProperty(this, "_uri", void 0);

    this._uri = uri;
  }

  var _proto = URI.prototype;

  _proto.toString = function toString() {
    return this._uri;
  };

  return URI;
}();

module.exports = URI;