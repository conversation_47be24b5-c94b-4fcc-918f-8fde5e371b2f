{"version": 3, "names": ["require", "_types", "Object", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "_exportNames", "exports", "defineProperty", "enumerable", "get", "_core", "_Screen", "_interopRequireWildcard", "_ScreenStackHeaderConfig", "_SearchBar", "_interopRequireDefault", "_ScreenContainer", "_ScreenStack", "_ScreenStackItem", "_FullWindowOverlay", "_ScreenFooter", "_ScreenContentWrapper", "_utils", "_useTransitionProgress", "e", "__esModule", "default", "_getRequireWildcardCache", "WeakMap", "r", "t", "has", "n", "__proto__", "a", "getOwnPropertyDescriptor", "u", "i", "set"], "sourceRoot": "../../src", "sources": ["index.tsx"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEAA,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AAAAE,MAAA,CAAAC,IAAA,CAAAF,MAAA,EAAAG,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAJ,MAAA,CAAAI,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAZ,MAAA,CAAAI,GAAA;IAAA;EAAA;AAAA;AAKA,IAAAS,KAAA,GAAAd,OAAA;AAUA,IAAAe,OAAA,GAAAC,uBAAA,CAAAhB,OAAA;AAMA,IAAAiB,wBAAA,GAAAjB,OAAA;AAUA,IAAAkB,UAAA,GAAAC,sBAAA,CAAAnB,OAAA;AACA,IAAAoB,gBAAA,GAAAD,sBAAA,CAAAnB,OAAA;AACA,IAAAqB,YAAA,GAAAF,sBAAA,CAAAnB,OAAA;AACA,IAAAsB,gBAAA,GAAAH,sBAAA,CAAAnB,OAAA;AACA,IAAAuB,kBAAA,GAAAJ,sBAAA,CAAAnB,OAAA;AACA,IAAAwB,aAAA,GAAAL,sBAAA,CAAAnB,OAAA;AACA,IAAAyB,qBAAA,GAAAN,sBAAA,CAAAnB,OAAA;AAKA,IAAA0B,MAAA,GAAA1B,OAAA;AASA,IAAA2B,sBAAA,GAAAR,sBAAA,CAAAnB,OAAA;AAA2E,SAAAmB,uBAAAS,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,yBAAAH,CAAA,6BAAAI,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAD,wBAAA,YAAAA,CAAAH,CAAA,WAAAA,CAAA,GAAAM,CAAA,GAAAD,CAAA,KAAAL,CAAA;AAAA,SAAAZ,wBAAAY,CAAA,EAAAK,CAAA,SAAAA,CAAA,IAAAL,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAE,OAAA,EAAAF,CAAA,QAAAM,CAAA,GAAAH,wBAAA,CAAAE,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAP,CAAA,UAAAM,CAAA,CAAArB,GAAA,CAAAe,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAApC,MAAA,CAAAS,cAAA,IAAAT,MAAA,CAAAqC,wBAAA,WAAAC,CAAA,IAAAZ,CAAA,oBAAAY,CAAA,OAAAjC,cAAA,CAAAC,IAAA,CAAAoB,CAAA,EAAAY,CAAA,SAAAC,CAAA,GAAAH,CAAA,GAAApC,MAAA,CAAAqC,wBAAA,CAAAX,CAAA,EAAAY,CAAA,UAAAC,CAAA,KAAAA,CAAA,CAAA5B,GAAA,IAAA4B,CAAA,CAAAC,GAAA,IAAAxC,MAAA,CAAAS,cAAA,CAAAyB,CAAA,EAAAI,CAAA,EAAAC,CAAA,IAAAL,CAAA,CAAAI,CAAA,IAAAZ,CAAA,CAAAY,CAAA,YAAAJ,CAAA,CAAAN,OAAA,GAAAF,CAAA,EAAAM,CAAA,IAAAA,CAAA,CAAAQ,GAAA,CAAAd,CAAA,EAAAQ,CAAA,GAAAA,CAAA", "ignoreList": []}