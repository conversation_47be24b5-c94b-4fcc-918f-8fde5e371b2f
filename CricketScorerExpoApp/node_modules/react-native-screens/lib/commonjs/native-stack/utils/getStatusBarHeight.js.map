{"version": 3, "names": ["_reactNative", "require", "getStatusBarHeight", "topInset", "dimensions", "isStatusBarTranslucent", "Platform", "OS", "hasDynamicIsland", "y"], "sourceRoot": "../../../../src", "sources": ["native-stack/utils/getStatusBarHeight.tsx"], "mappings": ";;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AAEe,SAASC,kBAAkBA,CACxCC,QAAgB,EAChBC,UAAgB,EAChBC,sBAA+B,EAC/B;EACA,IAAIC,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzB;IACA;IACA;IACA;IACA,MAAMC,gBAAgB,GAAGL,QAAQ,GAAG,EAAE;IACtC,OAAOK,gBAAgB,GAAGL,QAAQ,GAAG,CAAC,GAAGA,QAAQ;EACnD,CAAC,MAAM,IAAIG,qBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;IACpC;IACA,OAAOF,sBAAsB,GAAGF,QAAQ,GAAGC,UAAU,CAACK,CAAC;EACzD;EAEA,OAAON,QAAQ;AACjB", "ignoreList": []}