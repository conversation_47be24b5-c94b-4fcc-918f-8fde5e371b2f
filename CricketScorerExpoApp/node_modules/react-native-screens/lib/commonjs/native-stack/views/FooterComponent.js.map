{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_ScreenFooter", "e", "__esModule", "default", "FooterComponent", "children", "createElement", "collapsable"], "sourceRoot": "../../../../src", "sources": ["native-stack/views/FooterComponent.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAyD,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAM1C,SAASG,eAAeA,CAAC;EAAEC;AAAsB,CAAC,EAAE;EACjE,oBAAOR,MAAA,CAAAM,OAAA,CAAAG,aAAA,CAACN,aAAA,CAAAG,OAAY;IAACI,WAAW,EAAE;EAAM,GAAEF,QAAuB,CAAC;AACpE", "ignoreList": []}