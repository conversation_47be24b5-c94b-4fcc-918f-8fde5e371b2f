{"version": 3, "names": ["_native", "require", "React", "_interopRequireWildcard", "_reactNative", "_utils", "_ScreenStackHeaderConfig", "_SearchBar", "_interopRequireDefault", "_useBackPressSubscription", "_FontProcessor", "_warnOnce", "e", "__esModule", "default", "_getRequireWildcardCache", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "HeaderConfig", "backButtonImage", "backButtonInCustomView", "direction", "disableBackButtonMenu", "backButtonDisplayMode", "headerBackTitle", "headerBackTitleStyle", "headerBackTitleVisible", "headerCenter", "headerHideBackButton", "headerHideShadow", "headerLargeStyle", "headerLargeTitle", "headerLargeTitleHideShadow", "headerLargeTitleStyle", "headerLeft", "headerRight", "headerShown", "headerStyle", "headerTintColor", "headerTitle", "headerTitleStyle", "headerTopInsetEnabled", "headerTranslucent", "route", "searchBar", "title", "colors", "useTheme", "tintColor", "primary", "handleAttached", "handleDetached", "clearSubscription", "createSubscription", "useBackPressSubscription", "onBackPress", "executeNativeBackPress", "isDisabled", "disableBackButtonOverride", "backTitleFontFamily", "largeTitleFontFamily", "titleFontFamily", "processFonts", "fontFamily", "useEffect", "processedSearchBarOptions", "useMemo", "Platform", "OS", "onFocus", "args", "onClose", "isVisionOS", "isVision", "warnOnce", "color", "undefined", "createElement", "ScreenStackHeaderConfig", "backgroundColor", "card", "backTitle", "backTitleFontSize", "fontSize", "backTitleVisible", "blurEffect", "hidden", "hideBackButton", "hideShadow", "largeTitle", "largeTitleBackgroundColor", "largeTitleColor", "largeTitleFontSize", "largeTitleFontWeight", "fontWeight", "largeTitleHideShadow", "name", "titleColor", "text", "titleFontSize", "titleFontWeight", "topInsetEnabled", "translucent", "onAttached", "onDetached", "ScreenStackHeaderRightView", "ScreenStackHeaderBackButtonImage", "key", "source", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "isSearchBarAvailableForCurrentPlatform", "ScreenStackHeaderSearchBarView"], "sourceRoot": "../../../../src", "sources": ["native-stack/views/HeaderConfig.tsx"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,YAAA,GAAAH,OAAA;AAEA,IAAAI,MAAA,GAAAJ,OAAA;AAIA,IAAAK,wBAAA,GAAAL,OAAA;AAQA,IAAAM,UAAA,GAAAC,sBAAA,CAAAP,OAAA;AAEA,IAAAQ,yBAAA,GAAAR,OAAA;AACA,IAAAS,cAAA,GAAAT,OAAA;AACA,IAAAU,SAAA,GAAAH,sBAAA,CAAAP,OAAA;AAAiC,SAAAO,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,yBAAAH,CAAA,6BAAAI,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAD,wBAAA,YAAAA,CAAAH,CAAA,WAAAA,CAAA,GAAAM,CAAA,GAAAD,CAAA,KAAAL,CAAA;AAAA,SAAAT,wBAAAS,CAAA,EAAAK,CAAA,SAAAA,CAAA,IAAAL,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAE,OAAA,EAAAF,CAAA,QAAAM,CAAA,GAAAH,wBAAA,CAAAE,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAP,CAAA,UAAAM,CAAA,CAAAE,GAAA,CAAAR,CAAA,OAAAS,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAf,CAAA,oBAAAe,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAd,CAAA,EAAAe,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAf,CAAA,CAAAe,CAAA,YAAAN,CAAA,CAAAP,OAAA,GAAAF,CAAA,EAAAM,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAnB,CAAA,EAAAS,CAAA,GAAAA,CAAA;AAMlB,SAASW,YAAYA,CAAC;EACnCC,eAAe;EACfC,sBAAsB;EACtBC,SAAS;EACTC,qBAAqB;EACrBC,qBAAqB,GAAG,SAAS;EACjCC,eAAe;EACfC,oBAAoB,GAAG,CAAC,CAAC;EACzBC,sBAAsB,GAAG,IAAI;EAC7BC,YAAY;EACZC,oBAAoB;EACpBC,gBAAgB;EAChBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,gBAAgB;EAChBC,0BAA0B;EAC1BC,qBAAqB,GAAG,CAAC,CAAC;EAC1BC,UAAU;EACVC,WAAW;EACXC,WAAW;EACXC,WAAW,GAAG,CAAC,CAAC;EAChBC,eAAe;EACfC,WAAW;EACXC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,qBAAqB,GAAG,IAAI;EAC5BC,iBAAiB;EACjBC,KAAK;EACLC,SAAS;EACTC;AACK,CAAC,EAAe;EACrB,MAAM;IAAEC;EAAO,CAAC,GAAG,IAAAC,gBAAQ,EAAC,CAAC;EAC7B,MAAMC,SAAS,GAAGV,eAAe,IAAIQ,MAAM,CAACG,OAAO;;EAEnD;EACA;EACA;EACA,MAAM;IACJC,cAAc;IACdC,cAAc;IACdC,iBAAiB;IACjBC;EACF,CAAC,GAAG,IAAAC,kDAAwB,EAAC;IAC3BC,WAAW,EAAEC,6BAAsB;IACnCC,UAAU,EAAE,CAACb,SAAS,IAAI,CAAC,CAACA,SAAS,CAACc;EACxC,CAAC,CAAC;EAEF,MAAM,CAACC,mBAAmB,EAAEC,oBAAoB,EAAEC,eAAe,CAAC,GAChE,IAAAC,2BAAY,EAAC,CACXrC,oBAAoB,CAACsC,UAAU,EAC/B9B,qBAAqB,CAAC8B,UAAU,EAChCvB,gBAAgB,CAACuB,UAAU,CAC5B,CAAC;;EAEJ;EACA;EACA3E,KAAK,CAAC4E,SAAS,CAAC,MAAMZ,iBAAiB,EAAE,CAACR,SAAS,CAAC,CAAC;EAErD,MAAMqB,yBAAyB,GAAG7E,KAAK,CAAC8E,OAAO,CAAC,MAAM;IACpD,IACEC,qBAAQ,CAACC,EAAE,KAAK,SAAS,IACzBxB,SAAS,IACT,CAACA,SAAS,CAACc,yBAAyB,EACpC;MACA,MAAMW,OAAkC,GAAGA,CAAC,GAAGC,IAAI,KAAK;QACtDjB,kBAAkB,CAAC,CAAC;QACpBT,SAAS,CAACyB,OAAO,GAAG,GAAGC,IAAI,CAAC;MAC9B,CAAC;MACD,MAAMC,OAAkC,GAAGA,CAAC,GAAGD,IAAI,KAAK;QACtDlB,iBAAiB,CAAC,CAAC;QACnBR,SAAS,CAAC2B,OAAO,GAAG,GAAGD,IAAI,CAAC;MAC9B,CAAC;MAED,OAAO;QAAE,GAAG1B,SAAS;QAAEyB,OAAO;QAAEE;MAAQ,CAAC;IAC3C;IACA,OAAO3B,SAAS;EAClB,CAAC,EAAE,CAACA,SAAS,EAAES,kBAAkB,EAAED,iBAAiB,CAAC,CAAC;;EAEtD;EACA,MAAMoB,UAAU,GAAGL,qBAAQ,EAAEM,QAAQ;EAErC,IAAAC,iBAAQ,EACNF,UAAU,KACPhC,gBAAgB,CAACmC,KAAK,KAAKC,SAAS,IAAItC,eAAe,KAAKsC,SAAS,CAAC,EACzE,2EACF,CAAC;EAED,oBACExF,KAAA,CAAAyF,aAAA,CAACrF,wBAAA,CAAAsF,uBAAuB;IACtB1D,sBAAsB,EAAEA,sBAAuB;IAC/C2D,eAAe,EACb1C,WAAW,CAAC0C,eAAe,GAAG1C,WAAW,CAAC0C,eAAe,GAAGjC,MAAM,CAACkC,IACpE;IACDC,SAAS,EAAEzD,eAAgB;IAC3BmC,mBAAmB,EAAEA,mBAAoB;IACzCuB,iBAAiB,EAAEzD,oBAAoB,CAAC0D,QAAS;IACjDC,gBAAgB,EAAE1D,sBAAuB;IACzC2D,UAAU,EAAEhD,WAAW,CAACgD,UAAW;IACnCV,KAAK,EAAE3B,SAAU;IACjB3B,SAAS,EAAEA,SAAU;IACrBC,qBAAqB,EAAEA,qBAAsB;IAC7CC,qBAAqB,EAAEA,qBAAsB;IAC7C+D,MAAM,EAAElD,WAAW,KAAK,KAAM;IAC9BmD,cAAc,EAAE3D,oBAAqB;IACrC4D,UAAU,EAAE3D,gBAAiB;IAC7B4D,UAAU,EAAE1D,gBAAiB;IAC7B2D,yBAAyB,EAAE5D,gBAAgB,CAACiD,eAAgB;IAC5DY,eAAe,EAAE1D,qBAAqB,CAAC0C,KAAM;IAC7Cf,oBAAoB,EAAEA,oBAAqB;IAC3CgC,kBAAkB,EAAE3D,qBAAqB,CAACkD,QAAS;IACnDU,oBAAoB,EAAE5D,qBAAqB,CAAC6D,UAAW;IACvDC,oBAAoB,EAAE/D,0BAA2B;IACjDa,KAAK,EACHN,WAAW,KAAKqC,SAAS,GACrBrC,WAAW,GACXM,KAAK,KAAK+B,SAAS,GACnB/B,KAAK,GACLF,KAAK,CAACqD,IACX;IACDC,UAAU,EACRzD,gBAAgB,CAACmC,KAAK,KAAKC,SAAS,GAChCpC,gBAAgB,CAACmC,KAAK,GACtBrC,eAAe,KAAKsC,SAAS,GAC7BtC,eAAe,GACfQ,MAAM,CAACoD,IACZ;IACDrC,eAAe,EAAEA,eAAgB;IACjCsC,aAAa,EAAE3D,gBAAgB,CAAC2C,QAAS;IACzCiB,eAAe,EAAE5D,gBAAgB,CAACsD,UAAW;IAC7CO,eAAe,EAAE5D,qBAAsB;IACvC6D,WAAW,EAAE5D,iBAAiB,KAAK,IAAK;IACxC6D,UAAU,EAAErD,cAAe;IAC3BsD,UAAU,EAAErD;EAAe,GAC1BhB,WAAW,KAAKyC,SAAS,gBACxBxF,KAAA,CAAAyF,aAAA,CAACrF,wBAAA,CAAAiH,0BAA0B,QACxBtE,WAAW,CAAC;IAAEa;EAAU,CAAC,CACA,CAAC,GAC3B,IAAI,EACP7B,eAAe,KAAKyD,SAAS,gBAC5BxF,KAAA,CAAAyF,aAAA,CAACrF,wBAAA,CAAAkH,gCAAgC;IAC/BC,GAAG,EAAC,WAAW;IACfC,MAAM,EAAEzF;EAAgB,CACzB,CAAC,GACA,IAAI,EACPe,UAAU,KAAK0C,SAAS,gBACvBxF,KAAA,CAAAyF,aAAA,CAACrF,wBAAA,CAAAqH,yBAAyB,QACvB3E,UAAU,CAAC;IAAEc;EAAU,CAAC,CACA,CAAC,GAC1B,IAAI,EACPrB,YAAY,KAAKiD,SAAS,gBACzBxF,KAAA,CAAAyF,aAAA,CAACrF,wBAAA,CAAAsH,2BAA2B,QACzBnF,YAAY,CAAC;IAAEqB;EAAU,CAAC,CACA,CAAC,GAC5B,IAAI,EACP+D,6CAAsC,IACvC9C,yBAAyB,KAAKW,SAAS,gBACrCxF,KAAA,CAAAyF,aAAA,CAACrF,wBAAA,CAAAwH,8BAA8B,qBAE7B5H,KAAA,CAAAyF,aAAA,CAACpF,UAAA,CAAAO,OAAS,EAAKiE,yBAA4B,CACb,CAAC,GAC/B,IACmB,CAAC;AAE9B", "ignoreList": []}