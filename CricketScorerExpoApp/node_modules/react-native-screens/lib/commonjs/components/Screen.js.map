{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "ScreenContext", "InnerScreen", "_react", "_interopRequireDefault", "require", "_reactNative", "_TransitionProgressContext", "_DelayedFreeze", "_core", "_ScreenNativeComponent", "_ModalScreenNativeComponent", "_usePrevious", "_edgeToEdge", "_sheet", "e", "__esModule", "_extends", "assign", "bind", "n", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "AnimatedNativeScreen", "Animated", "createAnimatedComponent", "ScreenNativeComponent", "AnimatedNativeModalScreen", "ModalScreenNativeComponent", "React", "forwardRef", "props", "ref", "innerRef", "useRef", "useImperativeHandle", "current", "prevActivityState", "usePrevious", "activityState", "setRef", "onComponentRef", "closing", "Value", "progress", "goingForward", "enabled", "screensEnabled", "freezeOnBlur", "freezeEnabled", "shouldFreeze", "rest", "sheetAllowedDetents", "sheetLargestUndimmedDetentIndex", "SHEET_DIMMED_ALWAYS", "sheetGrabberVisible", "sheetCornerRadius", "sheetExpandsWhenScrolledToEdge", "sheetElevation", "sheetInitialDetentIndex", "stackPresentation", "onAppear", "onDisappear", "onWillAppear", "onWillDisappear", "isNativePlatformSupported", "resolvedSheetAllowedDetents", "resolveSheetAllowedDetents", "resolvedSheetLargestUndimmedDetent", "resolveSheetLargestUndimmedDetent", "resolvedSheetInitialDetentIndex", "resolveSheetInitialDetentIndex", "shouldUseModalScreenComponent", "Platform", "select", "ios", "undefined", "android", "AnimatedScreen", "active", "children", "isNativeStack", "gestureResponseDistance", "onGestureCancel", "style", "console", "warn", "Error", "handleRef", "viewConfig", "validAttributes", "display", "_viewConfig", "freeze", "createElement", "zIndex", "sheetLargestUndimmedDetent", "sheetInitialDetent", "start", "end", "top", "bottom", "onTransitionProgress", "event", "nativeEvent", "useNativeDriver", "Provider", "View", "createContext", "Screen", "ScreenWrapper", "useContext", "EDGE_TO_EDGE", "transformEdgeToEdgeProps", "displayName", "_default"], "sourceRoot": "../../../src", "sources": ["components/Screen.tsx"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA,GAAAF,OAAA,CAAAG,aAAA,GAAAH,OAAA,CAAAI,WAAA;AAEb,IAAAC,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,0BAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,cAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAGA,IAAAI,KAAA,GAAAJ,OAAA;AAOA,IAAAK,sBAAA,GAAAN,sBAAA,CAAAC,OAAA;AAGA,IAAAM,2BAAA,GAAAP,sBAAA,CAAAC,OAAA;AAIA,IAAAO,YAAA,GAAAP,OAAA;AACA,IAAAQ,WAAA,GAAAR,OAAA;AACA,IAAAS,MAAA,GAAAT,OAAA;AAKyB,SAAAD,uBAAAW,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAf,OAAA,EAAAe,CAAA;AAAA,SAAAE,SAAA,WAAAA,QAAA,GAAArB,MAAA,CAAAsB,MAAA,GAAAtB,MAAA,CAAAsB,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAL,CAAA,MAAAA,CAAA,GAAAM,SAAA,CAAAC,MAAA,EAAAP,CAAA,UAAAQ,CAAA,GAAAF,SAAA,CAAAN,CAAA,YAAAS,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAJ,CAAA,CAAAI,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAJ,CAAA,KAAAH,QAAA,CAAAU,KAAA,OAAAN,SAAA,KAfzB;AAkBA,MAAMO,oBAAoB,GAAGC,qBAAQ,CAACC,uBAAuB,CAC3DC,8BACF,CAAC;AACD,MAAMC,yBAAyB,GAAGH,qBAAQ,CAACC,uBAAuB,CAChEG,mCACF,CAAC;;AAED;AACA;;AAkBO,MAAM/B,WAAW,GAAAJ,OAAA,CAAAI,WAAA,gBAAGgC,cAAK,CAACC,UAAU,CACzC,SAASjC,WAAWA,CAACkC,KAAK,EAAEC,GAAG,EAAE;EAC/B,MAAMC,QAAQ,GAAGJ,cAAK,CAACK,MAAM,CAAoB,IAAI,CAAC;EACtDL,cAAK,CAACM,mBAAmB,CAACH,GAAG,EAAE,MAAMC,QAAQ,CAACG,OAAQ,EAAE,EAAE,CAAC;EAC3D,MAAMC,iBAAiB,GAAG,IAAAC,wBAAW,EAACP,KAAK,CAACQ,aAAa,CAAC;EAE1D,MAAMC,MAAM,GAAIR,GAAe,IAAK;IAClCC,QAAQ,CAACG,OAAO,GAAGJ,GAAG;IACtBD,KAAK,CAACU,cAAc,GAAGT,GAAG,CAAC;EAC7B,CAAC;EAED,MAAMU,OAAO,GAAGb,cAAK,CAACK,MAAM,CAAC,IAAIV,qBAAQ,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACP,OAAO;EAC3D,MAAMQ,QAAQ,GAAGf,cAAK,CAACK,MAAM,CAAC,IAAIV,qBAAQ,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACP,OAAO;EAC5D,MAAMS,YAAY,GAAGhB,cAAK,CAACK,MAAM,CAAC,IAAIV,qBAAQ,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACP,OAAO;EAEhE,MAAM;IACJU,OAAO,GAAG,IAAAC,oBAAc,EAAC,CAAC;IAC1BC,YAAY,GAAG,IAAAC,mBAAa,EAAC,CAAC;IAC9BC,YAAY;IACZ,GAAGC;EACL,CAAC,GAAGpB,KAAK;;EAET;EACA;EACA,MAAM;IACJ;IACAqB,mBAAmB,GAAG,CAAC,GAAG,CAAC;IAC3BC,+BAA+B,GAAGC,0BAAmB;IACrDC,mBAAmB,GAAG,KAAK;IAC3BC,iBAAiB,GAAG,CAAC,GAAG;IACxBC,8BAA8B,GAAG,IAAI;IACrCC,cAAc,GAAG,EAAE;IACnBC,uBAAuB,GAAG,CAAC;IAC3B;IACAC,iBAAiB;IACjB;IACAC,QAAQ;IACRC,WAAW;IACXC,YAAY;IACZC;EACF,CAAC,GAAGb,IAAI;EAER,IAAIL,OAAO,IAAImB,+BAAyB,EAAE;IACxC,MAAMC,2BAA2B,GAC/B,IAAAC,iCAA0B,EAACf,mBAAmB,CAAC;IACjD,MAAMgB,kCAAkC,GACtC,IAAAC,wCAAiC,EAC/BhB,+BAA+B,EAC/Ba,2BAA2B,CAACjD,MAAM,GAAG,CACvC,CAAC;IACH,MAAMqD,+BAA+B,GAAG,IAAAC,qCAA8B,EACpEZ,uBAAuB,EACvBO,2BAA2B,CAACjD,MAAM,GAAG,CACvC,CAAC;;IAED;IACA;IACA,MAAMuD,6BAA6B,GAAGC,qBAAQ,CAACC,MAAM,CAAC;MACpDC,GAAG,EAAE,EACHf,iBAAiB,KAAKgB,SAAS,IAC/BhB,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,gBAAgB,IACtCA,iBAAiB,KAAK,2BAA2B,CAClD;MACDiB,OAAO,EAAE,KAAK;MACdlF,OAAO,EAAE;IACX,CAAC,CAAC;IAEF,MAAMmF,cAAc,GAAGN,6BAA6B,GAChD7C,yBAAyB,GACzBJ,oBAAoB;IAExB,IAAI;MACF;MACA;MACA;MACAwD,MAAM;MACNxC,aAAa;MACbyC,QAAQ;MACRC,aAAa;MACbC,uBAAuB;MACvBC,eAAe;MACfC,KAAK;MACL,GAAGrD;IACL,CAAC,GAAGoB,IAAI;IAER,IAAI4B,MAAM,KAAKH,SAAS,IAAIrC,aAAa,KAAKqC,SAAS,EAAE;MACvDS,OAAO,CAACC,IAAI,CACV,+QACF,CAAC;MACD/C,aAAa,GAAGwC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACxC;IAEA,IACEE,aAAa,IACb5C,iBAAiB,KAAKuC,SAAS,IAC/BrC,aAAa,KAAKqC,SAAS,EAC3B;MACA,IAAIvC,iBAAiB,GAAGE,aAAa,EAAE;QACrC,MAAM,IAAIgD,KAAK,CACb,8DACF,CAAC;MACH;IACF;IAEA,MAAMC,SAAS,GAAIxD,GAAe,IAAK;MACrC;MACA;MACA,IAAIA,GAAG,EAAEyD,UAAU,EAAEC,eAAe,EAAEN,KAAK,EAAE;QAC3CpD,GAAG,CAACyD,UAAU,CAACC,eAAe,CAACN,KAAK,GAAG;UACrC,GAAGpD,GAAG,CAACyD,UAAU,CAACC,eAAe,CAACN,KAAK;UACvCO,OAAO,EAAE;QACX,CAAC;QACDnD,MAAM,CAACR,GAAG,CAAC;MACb,CAAC,MAAM,IAAIA,GAAG,EAAE4D,WAAW,EAAEF,eAAe,EAAEN,KAAK,EAAE;QACnDpD,GAAG,CAAC4D,WAAW,CAACF,eAAe,CAACN,KAAK,GAAG;UACtC,GAAGpD,GAAG,CAAC4D,WAAW,CAACF,eAAe,CAACN,KAAK;UACxCO,OAAO,EAAE;QACX,CAAC;QACDnD,MAAM,CAACR,GAAG,CAAC;MACb;IACF,CAAC;IAED,MAAM6D,MAAM,GACV7C,YAAY,KACXE,YAAY,KAAK0B,SAAS,GAAG1B,YAAY,GAAGX,aAAa,KAAK,CAAC,CAAC;IAEnE,oBACEzC,MAAA,CAAAH,OAAA,CAAAmG,aAAA,CAAC3F,cAAA,CAAAR,OAAa;MAACkG,MAAM,EAAEA;IAAO,gBAC5B/F,MAAA,CAAAH,OAAA,CAAAmG,aAAA,CAAChB,cAAc,EAAAlE,QAAA,KACTmB,KAAK;MACT;AACZ;AACA;AACA;AACA;MACY8B,QAAQ,EAAEA,QAAoC;MAC9CC,WAAW,EAAEA,WAA0C;MACvDC,YAAY,EAAEA,YAA4C;MAC1DC,eAAe,EAAEA,eAAkD;MACnEmB,eAAe,EACZA,eAAe,KACf,MAAM;QACL;MAAA,CACD;MAEH;MACA;MACA;MACA;MACA;MAAA;MACAC,KAAK,EAAE,CAACA,KAAK,EAAE;QAAEW,MAAM,EAAEnB;MAAU,CAAC,CAAE;MACtCrC,aAAa,EAAEA,aAAc;MAC7Ba,mBAAmB,EAAEc,2BAA4B;MACjD8B,0BAA0B,EAAE5B,kCAAmC;MAC/DV,cAAc,EAAEA,cAAe;MAC/BH,mBAAmB,EAAEA,mBAAoB;MACzCC,iBAAiB,EAAEA,iBAAkB;MACrCC,8BAA8B,EAAEA,8BAA+B;MAC/DwC,kBAAkB,EAAE3B,+BAAgC;MACpDY,uBAAuB,EAAE;QACvBgB,KAAK,EAAEhB,uBAAuB,EAAEgB,KAAK,IAAI,CAAC,CAAC;QAC3CC,GAAG,EAAEjB,uBAAuB,EAAEiB,GAAG,IAAI,CAAC,CAAC;QACvCC,GAAG,EAAElB,uBAAuB,EAAEkB,GAAG,IAAI,CAAC,CAAC;QACvCC,MAAM,EAAEnB,uBAAuB,EAAEmB,MAAM,IAAI,CAAC;MAC9C;MACA;MACA;MAAA;MACArE,GAAG,EAAEwD,SAAU;MACfc,oBAAoB,EAClB,CAACrB,aAAa,GACVL,SAAS,GACTpD,qBAAQ,CAAC+E,KAAK,CACZ,CACE;QACEC,WAAW,EAAE;UACX5D,QAAQ;UACRF,OAAO;UACPG;QACF;MACF,CAAC,CACF,EACD;QAAE4D,eAAe,EAAE;MAAK,CAC1B;IACL,IACA,CAACxB,aAAa;IAAK;IAClBD,QAAQ,gBAERlF,MAAA,CAAAH,OAAA,CAAAmG,aAAA,CAAC5F,0BAAA,CAAAP,OAAyB,CAAC+G,QAAQ;MACjChH,KAAK,EAAE;QACLkD,QAAQ;QACRF,OAAO;QACPG;MACF;IAAE,GACDmC,QACiC,CAExB,CACH,CAAC;EAEpB,CAAC,MAAM;IACL;IACA,IAAI;MACFD,MAAM;MACNxC,aAAa;MACb6C,KAAK;MACL;MACA3C,cAAc;MACd,GAAGV;IACL,CAAC,GAAGoB,IAAI;IAER,IAAI4B,MAAM,KAAKH,SAAS,IAAIrC,aAAa,KAAKqC,SAAS,EAAE;MACvDrC,aAAa,GAAGwC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;IACtC;IACA,oBACEjF,MAAA,CAAAH,OAAA,CAAAmG,aAAA,CAAC7F,YAAA,CAAAuB,QAAQ,CAACmF,IAAI,EAAA/F,QAAA;MACZwE,KAAK,EAAE,CAACA,KAAK,EAAE;QAAEO,OAAO,EAAEpD,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG;MAAO,CAAC,CAAE;MACnEP,GAAG,EAAEQ;IAAO,GACRT,KAAK,CACV,CAAC;EAEN;AACF,CACF,CAAC;;AAED;AACA;AACO,MAAMnC,aAAa,GAAAH,OAAA,CAAAG,aAAA,gBAAGiC,cAAK,CAAC+E,aAAa,CAAC/G,WAAW,CAAC;AAE7D,MAAMgH,MAAM,gBAAGhF,cAAK,CAACC,UAAU,CAAoB,CAACC,KAAK,EAAEC,GAAG,KAAK;EACjE,MAAM8E,aAAa,GAAGjF,cAAK,CAACkF,UAAU,CAACnH,aAAa,CAAC,IAAIC,WAAW;EAEpE,oBACEC,MAAA,CAAAH,OAAA,CAAAmG,aAAA,CAACgB,aAAa,EAAAlG,QAAA,KACPoG,wBAAY,GAAG,IAAAC,oCAAwB,EAAClF,KAAK,CAAC,GAAGA,KAAK;IAC3DC,GAAG,EAAEA;EAAI,EACV,CAAC;AAEN,CAAC,CAAC;AAEF6E,MAAM,CAACK,WAAW,GAAG,QAAQ;AAAC,IAAAC,QAAA,GAAA1H,OAAA,CAAAE,OAAA,GAEfkH,MAAM", "ignoreList": []}