{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_FullWindowOverlayNativeComponent", "e", "__esModule", "default", "NativeFullWindowOverlay", "FullWindowOverlayNativeComponent", "FullWindowOverlay", "props", "width", "height", "useWindowDimensions", "Platform", "OS", "console", "warn", "createElement", "View", "style", "StyleSheet", "absoluteFill", "accessibilityContainerViewIsModal", "unstable_accessibilityContainerViewIsModal", "children", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["components/FullWindowOverlay.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAUA,IAAAE,iCAAA,GAAAH,sBAAA,CAAAC,OAAA;AAA0F,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAD1F;;AAIA,MAAMG,uBAKL,GAAGC,yCAAuC;AAO3C,SAASC,iBAAiBA,CAACC,KAA6B,EAAE;EACxD,MAAM;IAAEC,KAAK;IAAEC;EAAO,CAAC,GAAG,IAAAC,gCAAmB,EAAC,CAAC;EAC/C,IAAIC,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzBC,OAAO,CAACC,IAAI,CAAC,uDAAuD,CAAC;IACrE,oBAAOlB,MAAA,CAAAO,OAAA,CAAAY,aAAA,CAAChB,YAAA,CAAAiB,IAAI,EAAKT,KAAQ,CAAC;EAC5B;EACA,oBACEX,MAAA,CAAAO,OAAA,CAAAY,aAAA,CAACX,uBAAuB;IACtBa,KAAK,EAAE,CAACC,uBAAU,CAACC,YAAY,EAAE;MAAEX,KAAK;MAAEC;IAAO,CAAC,CAAE;IACpDW,iCAAiC,EAC/Bb,KAAK,CAACc;EACP,GACAd,KAAK,CAACe,QACgB,CAAC;AAE9B;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAArB,OAAA,GAEcG,iBAAiB", "ignoreList": []}