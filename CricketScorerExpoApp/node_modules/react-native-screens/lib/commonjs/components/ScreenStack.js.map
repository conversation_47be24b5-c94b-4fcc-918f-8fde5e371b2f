{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_react", "_interopRequireDefault", "require", "_contexts", "_warnOnce", "_ScreenStackNativeComponent", "e", "__esModule", "_extends", "assign", "bind", "n", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "assertGHProvider", "ScreenGestureDetector", "goBackGesture", "isGestureDetectorProviderNotDetected", "name", "undefined", "warnOnce", "assertCustomScreenTransitionsProps", "screensRefs", "currentScreenId", "isGestureDetectorNotConfiguredProperly", "ScreenStack", "props", "passedScreenRefs", "transitionAnimation", "screenEdgeGesture", "onFinishTransitioning", "children", "rest", "React", "useRef", "current", "ref", "useContext", "GHContext", "gestureDetectorBridge", "stackUseEffectCallback", "_stackRef", "useEffect", "createElement", "RNSScreensRefContext", "Provider", "_default"], "sourceRoot": "../../../src", "sources": ["components/ScreenStack.tsx"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAEb,IAAAC,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAQA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAH,sBAAA,CAAAC,OAAA;AAGA,IAAAG,2BAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAE8C,SAAAD,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAP,OAAA,EAAAO,CAAA;AAAA,SAAAE,SAAA,WAAAA,QAAA,GAAAb,MAAA,CAAAc,MAAA,GAAAd,MAAA,CAAAc,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAL,CAAA,MAAAA,CAAA,GAAAM,SAAA,CAAAC,MAAA,EAAAP,CAAA,UAAAQ,CAAA,GAAAF,SAAA,CAAAN,CAAA,YAAAS,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAJ,CAAA,CAAAI,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAJ,CAAA,KAAAH,QAAA,CAAAU,KAAA,OAAAN,SAAA,KAH9C;AAKA,MAAMO,gBAAgB,GAAGA,CACvBC,qBAEsB,EACtBC,aAAwC,KACrC;EACH,MAAMC,oCAAoC,GACxCF,qBAAqB,CAACG,IAAI,KAAK,WAAW,IAAIF,aAAa,KAAKG,SAAS;EAE3E,IAAAC,iBAAQ,EACNH,oCAAoC,EACpC,8IACF,CAAC;AACH,CAAC;AAED,MAAMI,kCAAkC,GAAGA,CACzCC,WAA4C,EAC5CC,eAAoD,EACpDP,aAAgD,KAC7C;EACH,MAAMQ,sCAAsC,GAC1CR,aAAa,KAAKG,SAAS,IAC3BG,WAAW,KAAK,IAAI,IACpBC,eAAe,KAAKJ,SAAS;EAE/B,IAAAC,iBAAQ,EACNI,sCAAsC,EACtC,kFACF,CAAC;AACH,CAAC;AAED,SAASC,WAAWA,CAACC,KAAuB,EAAE;EAC5C,MAAM;IACJV,aAAa;IACbM,WAAW,EAAEK,gBAAgB;IAAE;IAC/BJ,eAAe;IACfK,mBAAmB;IACnBC,iBAAiB;IACjBC,qBAAqB;IACrBC,QAAQ;IACR,GAAGC;EACL,CAAC,GAAGN,KAAK;EAET,MAAMJ,WAAW,GAAGW,cAAK,CAACC,MAAM,CAC9BP,gBAAgB,EAAEQ,OAAO,IAAI,CAAC,CAChC,CAAC;EACD,MAAMC,GAAG,GAAGH,cAAK,CAACC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMnB,qBAAqB,GAAGkB,cAAK,CAACI,UAAU,CAACC,mBAAS,CAAC;EACzD,MAAMC,qBAAqB,GAAGN,cAAK,CAACC,MAAM,CAAwB;IAChEM,sBAAsB,EAAEC,SAAS,IAAI;MACnC;IAAA;EAEJ,CAAC,CAAC;EAEFR,cAAK,CAACS,SAAS,CAAC,MAAM;IACpBH,qBAAqB,CAACJ,OAAO,CAACK,sBAAsB,CAACJ,GAAG,CAAC;EAC3D,CAAC,CAAC;EAEFtB,gBAAgB,CAACC,qBAAqB,EAAEC,aAAa,CAAC;EAEtDK,kCAAkC,CAChCC,WAAW,EACXC,eAAe,EACfP,aACF,CAAC;EAED,oBACErB,MAAA,CAAAD,OAAA,CAAAiD,aAAA,CAAC7C,SAAA,CAAA8C,oBAAoB,CAACC,QAAQ;IAACpD,KAAK,EAAE6B;EAAY,gBAChD3B,MAAA,CAAAD,OAAA,CAAAiD,aAAA,CAAC5B,qBAAqB;IACpBwB,qBAAqB,EAAEA,qBAAsB;IAC7CvB,aAAa,EAAEA,aAAc;IAC7BY,mBAAmB,EAAEA,mBAAoB;IACzCC,iBAAiB,EAAEA,iBAAiB,IAAI,KAAM;IAC9CP,WAAW,EAAEA,WAAY;IACzBC,eAAe,EAAEA;EAAgB,gBACjC5B,MAAA,CAAAD,OAAA,CAAAiD,aAAA,CAAC3C,2BAAA,CAAAN,OAA0B,EAAAS,QAAA,KACrB6B,IAAI;IACR;AACV;AACA;AACA;AACA;IACUF,qBAAqB,EACnBA,qBACD;IACDM,GAAG,EAAEA;EAAI,IACRL,QACyB,CACP,CACM,CAAC;AAEpC;AAAC,IAAAe,QAAA,GAAAtD,OAAA,CAAAE,OAAA,GAEc+B,WAAW", "ignoreList": []}