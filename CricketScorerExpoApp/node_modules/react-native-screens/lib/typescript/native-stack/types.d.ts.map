{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../../src/native-stack/types.tsx"], "names": [], "mappings": "AAAA,OAAO,EACL,uBAAuB,EACvB,UAAU,EACV,iBAAiB,EACjB,cAAc,EACd,aAAa,EACb,oBAAoB,EACpB,kBAAkB,EAClB,kBAAkB,EAClB,SAAS,EACV,MAAM,0BAA0B,CAAC;AAClC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,iBAAiB,EAAE,MAAM,OAAO,CAAC;AAC1C,OAAO,EACL,mBAAmB,EACnB,SAAS,EACT,SAAS,EACT,UAAU,EACX,MAAM,cAAc,CAAC;AACtB,OAAO,EACL,qBAAqB,EACrB,WAAW,EACX,4BAA4B,EAC5B,cAAc,EACf,MAAM,UAAU,CAAC;AAElB;;GAEG;AACH,MAAM,MAAM,6BAA6B,GAAG;IAC1C;;;;OAIG;IACH,MAAM,EAAE;QAAE,IAAI,EAAE,SAAS,CAAA;KAAE,CAAC;IAC5B;;OAEG;IACH,OAAO,EAAE;QAAE,IAAI,EAAE,SAAS,CAAA;KAAE,CAAC;IAC7B;;OAEG;IACH,eAAe,EAAE;QAAE,IAAI,EAAE;YAAE,OAAO,EAAE,OAAO,CAAA;SAAE,CAAA;KAAE,CAAC;IAChD;;OAEG;IACH,aAAa,EAAE;QAAE,IAAI,EAAE;YAAE,OAAO,EAAE,OAAO,CAAA;SAAE,CAAA;KAAE,CAAC;IAC9C;;OAEG;IACH,aAAa,EAAE;QAAE,IAAI,EAAE,SAAS,CAAA;KAAE,CAAC;IACnC;;OAEG;IACH,kBAAkB,EAAE;QAAE,IAAI,EAAE;YAAE,YAAY,EAAE,MAAM,CAAA;SAAE,CAAA;KAAE,CAAC;IACvD;;;;;;;OAOG;IACH,iBAAiB,EAAE;QAAE,IAAI,EAAE;YAAE,KAAK,EAAE,MAAM,CAAC;YAAC,QAAQ,EAAE,OAAO,CAAA;SAAE,CAAA;KAAE,CAAC;CACnE,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,yBAAyB,CACnC,SAAS,SAAS,aAAa,EAC/B,SAAS,SAAS,MAAM,SAAS,GAAG,MAAM,IACxC,cAAc,CAChB,SAAS,EACT,SAAS,EACT,oBAAoB,CAAC,SAAS,CAAC,EAC/B,4BAA4B,EAC5B,6BAA6B,CAC9B,GACC,kBAAkB,CAAC,SAAS,CAAC,CAAC;AAEhC;;GAEG;AACH,MAAM,MAAM,sBAAsB,CAChC,SAAS,SAAS,aAAa,EAC/B,SAAS,SAAS,MAAM,SAAS,GAAG,MAAM,IACxC;IACF,UAAU,EAAE,yBAAyB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IAC5D,KAAK,EAAE,SAAS,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;CACxC,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,4BAA4B,GAAG,iBAAiB,CAC1D,aAAa,EACb,6BAA6B,CAC9B,CAAC;AAEF;;;GAGG;AACH,MAAM,MAAM,2BAA2B,GAAG,EAAE,CAAC;AAE7C;;GAEG;AACH,MAAM,MAAM,4BAA4B,GAAG;IACzC;;;OAGG;IACH,eAAe,CAAC,EAAE,mBAAmB,CAAC;IACtC;;OAEG;IACH,sBAAsB,CAAC,EAAE,OAAO,CAAC;IACjC;;;;OAIG;IACH,YAAY,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IACpC;;;;OAIG;IACH,sBAAsB,CAAC,EAAE,OAAO,CAAC;IACjC;;OAEG;IACH,SAAS,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC;IAC1B;;;OAGG;IACH,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC;;;;;;;;;OASG;IACH,qBAAqB,CAAC,EAAE,4BAA4B,CAAC,uBAAuB,CAAC,CAAC;IAC9E;;;OAGG;IACH,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB;;;;;;OAMG;IACH,sBAAsB,CAAC,EAAE,OAAO,CAAC;IACjC;;;;;;;;OAQG;IACH,4BAA4B,CAAC,EAAE,OAAO,CAAC;IACvC;;;;;OAKG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB;;;;OAIG;IACH,uBAAuB,CAAC,EAAE,WAAW,CAAC,yBAAyB,CAAC,CAAC;IACjE;;;;;OAKG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB;;;;;;;;OAQG;IACH,oBAAoB,CAAC,EAAE;QACrB,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,QAAQ,CAAC,EAAE,MAAM,CAAC;KACnB,CAAC;IACF;;;;;;;;;OASG;IACH,sBAAsB,CAAC,EAAE,OAAO,CAAC;IACjC;;OAEG;IACH,YAAY,CAAC,EAAE,CAAC,KAAK,EAAE;QAAE,SAAS,CAAC,EAAE,UAAU,CAAA;KAAE,KAAK,KAAK,CAAC,SAAS,CAAC;IACtE;;OAEG;IACH,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAC/B;;OAEG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B;;;;;OAKG;IACH,gBAAgB,CAAC,EAAE;QACjB,eAAe,CAAC,EAAE,UAAU,CAAC;KAC9B,CAAC;IACF;;;;;;;OAOG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B;;OAEG;IACH,0BAA0B,CAAC,EAAE,OAAO,CAAC;IACrC;;;;;;;;;OASG;IACH,qBAAqB,CAAC,EAAE;QACtB,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,KAAK,CAAC,EAAE,UAAU,CAAC;KACpB,CAAC;IACF;;OAEG;IACH,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE;QAAE,SAAS,CAAC,EAAE,UAAU,CAAA;KAAE,KAAK,KAAK,CAAC,SAAS,CAAC;IACpE;;OAEG;IACH,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE;QAAE,SAAS,CAAC,EAAE,UAAU,CAAA;KAAE,KAAK,KAAK,CAAC,SAAS,CAAC;IACrE;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB;;;;OAIG;IACH,WAAW,CAAC,EAAE;QACZ,eAAe,CAAC,EAAE,UAAU,CAAC;QAC7B,UAAU,CAAC,EAAE,4BAA4B,CAAC,YAAY,CAAC,CAAC;KACzD,CAAC;IACF;;OAEG;IACH,eAAe,CAAC,EAAE,UAAU,CAAC;IAC7B;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB;;;;;;OAMG;IACH,gBAAgB,CAAC,EAAE;QACjB,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,KAAK,CAAC,EAAE,UAAU,CAAC;KACpB,CAAC;IACF;;;;;;;OAOG;IACH,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC;;OAEG;IACH,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAC5B;;;;OAIG;IACH,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAC9B;;;;OAIG;IACH,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAC9B;;;;;;OAMG;IACH,gCAAgC,CAAC,EAAE,OAAO,CAAC;IAC3C;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,UAAU,CAAC;IAChC;;;;OAIG;IACH,wBAAwB,CAAC,EAAE,OAAO,CAAC;IACnC;;;;OAIG;IACH,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAC9B;;;;;OAKG;IACH,gBAAgB,CAAC,EAAE,WAAW,CAAC,kBAAkB,CAAC,CAAC;IACnD;;;;;;;;;;;OAWG;IACH,iBAAiB,CAAC,EAAE,WAAW,CAAC,mBAAmB,CAAC,CAAC;IACrD;;OAEG;IACH,SAAS,CAAC,EAAE,cAAc,CAAC;IAC3B;;;;;;;;;OASG;IACH,mBAAmB,CAAC,EAAE,WAAW,CAAC,qBAAqB,CAAC,GAAG,eAAe,CAAC;IAC3E;;;;;;;;OAQG;IACH,cAAc,CAAC,EAAE,WAAW,CAAC,gBAAgB,CAAC,CAAC;IAC/C;;;;;;OAMG;IACH,8BAA8B,CAAC,EAAE,WAAW,CAAC,gCAAgC,CAAC,CAAC;IAC/E;;;;;;;OAOG;IACH,iBAAiB,CAAC,EAAE,WAAW,CAAC,mBAAmB,CAAC,CAAC;IACrD;;;;;;OAMG;IACH,mBAAmB,CAAC,EAAE,WAAW,CAAC,qBAAqB,CAAC,CAAC;IACzD;;;;;OAKG;IACH,uBAAuB,CAAC,EAAE,WAAW,CAAC,yBAAyB,CAAC,CAAC;IACjE;;;;;;;;;;;;;;;;OAgBG;IACH,+BAA+B,CAAC,EAAE,WAAW,CAAC,iCAAiC,CAAC,CAAC;IACjF;;;;;;;;;;;;;;OAcG;IACH,cAAc,CAAC,EAAE,WAAW,CAAC,gBAAgB,CAAC,CAAC;IAC/C;;;;;;;;;;;OAWG;IACH,iBAAiB,CAAC,EAAE,WAAW,CAAC,mBAAmB,CAAC,CAAC;IACrD;;OAEG;IACH,kBAAkB,CAAC,EAAE,WAAW,CAAC,oBAAoB,CAAC,CAAC;IACvD;;;;OAIG;IACH,cAAc,CAAC,EAAE,UAAU,CAAC;IAC5B;;OAEG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B;;OAEG;IACH,cAAc,CAAC,EAAE,WAAW,CAAC,gBAAgB,CAAC,CAAC;IAC/C;;;;OAIG;IACH,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAC/B;;;;;;;OAOG;IACH,cAAc,CAAC,EAAE,WAAW,CAAC,gBAAgB,CAAC,CAAC;IAC/C;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IACf;;;;;OAKG;IACH,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAE5B,aAAa,CAAC,EAAE,aAAa,CAAC;IAC9B,mBAAmB,CAAC,EAAE,wBAAwB,CAAC;IAC/C,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAC5B;;;;;;;;;;OAUG;IACH,oBAAoB,CAAC,EAAE,MAAM,KAAK,CAAC,SAAS,CAAC;CAC9C,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,yBAAyB,GACnC,uBAAuB,CAAC,4BAA4B,CAAC,GACnD,kBAAkB,GAClB,2BAA2B,CAAC;AAEhC;;GAEG;AACH,MAAM,MAAM,qBAAqB,GAAG,UAAU,CAC5C,aAAa,EACb,MAAM,EACN,oBAAoB,CAAC,aAAa,CAAC,EACnC,4BAA4B,CAC7B,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,wBAAwB,GAAG;IACrC,CAAC,GAAG,EAAE,MAAM,GAAG,qBAAqB,CAAC;CACtC,CAAC;AAEF;;GAEG;AAEH;;;GAGG;AACH,MAAM,MAAM,6BAA6B,GAAG;IAC1C,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;IAClB,YAAY,EAAE,MAAM,CAAC;IACrB,YAAY,EAAE,MAAM,CAAC;IACrB,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;CACnB,CAAC;AAEF;;;GAGG;AACH,MAAM,MAAM,aAAa,GACrB,YAAY,GACZ,WAAW,GACX,SAAS,GACT,WAAW,GACX,eAAe,GACf,iBAAiB,GACjB,qBAAqB,CAAC;AAE1B;;GAEG;AACH,MAAM,WAAW,kBAAkB;IACjC,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,MAAM,MAAM,wBAAwB,GAAG;IACrC,cAAc,EAAE,CACd,KAAK,EAAE,6BAA6B,EACpC,UAAU,EAAE,kBAAkB,KAC3B,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC7B,mBAAmB,EAAE,CACnB,KAAK,EAAE,6BAA6B,EACpC,UAAU,EAAE,kBAAkB,KAC3B,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;CAC9B,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,iBAAiB,GAAG,KAAK,CAAC,gBAAgB,CACpD,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CACnE,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,oBAAoB,GAAG,iBAAiB,CAAC;IACnD,qBAAqB,EAAE,KAAK,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC;IACrE,WAAW,EAAE,iBAAiB,CAAC;IAC/B,eAAe,EAAE,MAAM,CAAC;IACxB,aAAa,EAAE,aAAa,GAAG,SAAS,CAAC;IACzC,mBAAmB,EAAE,wBAAwB,GAAG,SAAS,CAAC;IAC1D,iBAAiB,EAAE,OAAO,GAAG,SAAS,CAAC;CACxC,CAAC,CAAC"}