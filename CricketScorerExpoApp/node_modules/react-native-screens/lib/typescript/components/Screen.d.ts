import React from 'react';
import { View } from 'react-native';
import { ScreenProps } from '../types';
export declare const InnerScreen: React.ForwardRefExoticComponent<Omit<ScreenProps, "ref"> & React.RefAttributes<View>>;
export declare const ScreenContext: React.Context<React.ForwardRefExoticComponent<Omit<ScreenProps, "ref"> & React.RefAttributes<View>>>;
declare const Screen: React.ForwardRefExoticComponent<Omit<ScreenProps, "ref"> & React.RefAttributes<View>>;
export default Screen;
//# sourceMappingURL=Screen.d.ts.map