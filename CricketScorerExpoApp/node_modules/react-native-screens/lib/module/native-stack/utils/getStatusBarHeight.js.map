{"version": 3, "names": ["Platform", "getStatusBarHeight", "topInset", "dimensions", "isStatusBarTranslucent", "OS", "hasDynamicIsland", "y"], "sourceRoot": "../../../../src", "sources": ["native-stack/utils/getStatusBarHeight.tsx"], "mappings": "AACA,SAASA,QAAQ,QAAQ,cAAc;AAEvC,eAAe,SAASC,kBAAkBA,CACxCC,QAAgB,EAChBC,UAAgB,EAChBC,sBAA+B,EAC/B;EACA,IAAIJ,QAAQ,CAACK,EAAE,KAAK,KAAK,EAAE;IACzB;IACA;IACA;IACA;IACA,MAAMC,gBAAgB,GAAGJ,QAAQ,GAAG,EAAE;IACtC,OAAOI,gBAAgB,GAAGJ,QAAQ,GAAG,CAAC,GAAGA,QAAQ;EACnD,CAAC,MAAM,IAAIF,QAAQ,CAACK,EAAE,KAAK,SAAS,EAAE;IACpC;IACA,OAAOD,sBAAsB,GAAGF,QAAQ,GAAGC,UAAU,CAACI,CAAC;EACzD;EAEA,OAAOL,QAAQ;AACjB", "ignoreList": []}