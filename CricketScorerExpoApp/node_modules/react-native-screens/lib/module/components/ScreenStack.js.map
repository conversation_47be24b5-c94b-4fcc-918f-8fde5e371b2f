{"version": 3, "names": ["_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "React", "GHContext", "RNSScreensRefContext", "warnOnce", "ScreenStackNativeComponent", "assertGHProvider", "ScreenGestureDetector", "goBackGesture", "isGestureDetectorProviderNotDetected", "name", "undefined", "assertCustomScreenTransitionsProps", "screensRefs", "currentScreenId", "isGestureDetectorNotConfiguredProperly", "ScreenStack", "props", "passedScreenRefs", "transitionAnimation", "screenEdgeGesture", "onFinishTransitioning", "children", "rest", "useRef", "current", "ref", "useContext", "gestureDetectorBridge", "stackUseEffectCallback", "_stackRef", "useEffect", "createElement", "Provider", "value"], "sourceRoot": "../../../src", "sources": ["components/ScreenStack.tsx"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,SAAA,WAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,CAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAL,CAAA,CAAAK,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAL,CAAA,KAAAJ,QAAA,CAAAY,KAAA,OAAAN,SAAA;AAEb,OAAOO,KAAK,MAA6B,OAAO;AAQhD,SAASC,SAAS,EAAEC,oBAAoB,QAAQ,aAAa;AAC7D,OAAOC,QAAQ,MAAM,WAAW;;AAEhC;AACA,OAAOC,0BAA0B,MAE1B,sCAAsC;AAE7C,MAAMC,gBAAgB,GAAGA,CACvBC,qBAEsB,EACtBC,aAAwC,KACrC;EACH,MAAMC,oCAAoC,GACxCF,qBAAqB,CAACG,IAAI,KAAK,WAAW,IAAIF,aAAa,KAAKG,SAAS;EAE3EP,QAAQ,CACNK,oCAAoC,EACpC,8IACF,CAAC;AACH,CAAC;AAED,MAAMG,kCAAkC,GAAGA,CACzCC,WAA4C,EAC5CC,eAAoD,EACpDN,aAAgD,KAC7C;EACH,MAAMO,sCAAsC,GAC1CP,aAAa,KAAKG,SAAS,IAC3BE,WAAW,KAAK,IAAI,IACpBC,eAAe,KAAKH,SAAS;EAE/BP,QAAQ,CACNW,sCAAsC,EACtC,kFACF,CAAC;AACH,CAAC;AAED,SAASC,WAAWA,CAACC,KAAuB,EAAE;EAC5C,MAAM;IACJT,aAAa;IACbK,WAAW,EAAEK,gBAAgB;IAAE;IAC/BJ,eAAe;IACfK,mBAAmB;IACnBC,iBAAiB;IACjBC,qBAAqB;IACrBC,QAAQ;IACR,GAAGC;EACL,CAAC,GAAGN,KAAK;EAET,MAAMJ,WAAW,GAAGZ,KAAK,CAACuB,MAAM,CAC9BN,gBAAgB,EAAEO,OAAO,IAAI,CAAC,CAChC,CAAC;EACD,MAAMC,GAAG,GAAGzB,KAAK,CAACuB,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMjB,qBAAqB,GAAGN,KAAK,CAAC0B,UAAU,CAACzB,SAAS,CAAC;EACzD,MAAM0B,qBAAqB,GAAG3B,KAAK,CAACuB,MAAM,CAAwB;IAChEK,sBAAsB,EAAEC,SAAS,IAAI;MACnC;IAAA;EAEJ,CAAC,CAAC;EAEF7B,KAAK,CAAC8B,SAAS,CAAC,MAAM;IACpBH,qBAAqB,CAACH,OAAO,CAACI,sBAAsB,CAACH,GAAG,CAAC;EAC3D,CAAC,CAAC;EAEFpB,gBAAgB,CAACC,qBAAqB,EAAEC,aAAa,CAAC;EAEtDI,kCAAkC,CAChCC,WAAW,EACXC,eAAe,EACfN,aACF,CAAC;EAED,oBACEP,KAAA,CAAA+B,aAAA,CAAC7B,oBAAoB,CAAC8B,QAAQ;IAACC,KAAK,EAAErB;EAAY,gBAChDZ,KAAA,CAAA+B,aAAA,CAACzB,qBAAqB;IACpBqB,qBAAqB,EAAEA,qBAAsB;IAC7CpB,aAAa,EAAEA,aAAc;IAC7BW,mBAAmB,EAAEA,mBAAoB;IACzCC,iBAAiB,EAAEA,iBAAiB,IAAI,KAAM;IAC9CP,WAAW,EAAEA,WAAY;IACzBC,eAAe,EAAEA;EAAgB,gBACjCb,KAAA,CAAA+B,aAAA,CAAC3B,0BAA0B,EAAAjB,QAAA,KACrBmC,IAAI;IACR;AACV;AACA;AACA;AACA;IACUF,qBAAqB,EACnBA,qBACD;IACDK,GAAG,EAAEA;EAAI,IACRJ,QACyB,CACP,CACM,CAAC;AAEpC;AAEA,eAAeN,WAAW", "ignoreList": []}