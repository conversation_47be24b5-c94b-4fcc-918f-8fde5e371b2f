{"version": 3, "names": ["useEffect", "useRef", "usePrevious", "state", "ref", "current"], "sourceRoot": "../../../../src", "sources": ["components/helpers/usePrevious.tsx"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAEzC,OAAO,SAASC,WAAWA,CAAIC,KAAQ,EAAiB;EACtD,MAAMC,GAAG,GAAGH,MAAM,CAAI,CAAC;EAEvBD,SAAS,CAAC,MAAM;IACdI,GAAG,CAACC,OAAO,GAAGF,KAAK;EACrB,CAAC,CAAC;EAEF,OAAOC,GAAG,CAACC,OAAO;AACpB", "ignoreList": []}