{"version": 3, "names": ["React", "Freeze", "DelayedFreeze", "freeze", "children", "freezeState", "setFreezeState", "useState", "useEffect", "id", "setImmediate", "clearImmediate", "createElement"], "sourceRoot": "../../../../src", "sources": ["components/helpers/DelayedFreeze.tsx"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,cAAc;AAOrC;AACA;AACA,SAASC,aAAaA,CAAC;EAAEC,MAAM;EAAEC;AAA6B,CAAC,EAAE;EAC/D;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGN,KAAK,CAACO,QAAQ,CAAC,KAAK,CAAC;EAE3DP,KAAK,CAACQ,SAAS,CAAC,MAAM;IACpB,MAAMC,EAAE,GAAGC,YAAY,CAAC,MAAM;MAC5BJ,cAAc,CAACH,MAAM,CAAC;IACxB,CAAC,CAAC;IACF,OAAO,MAAM;MACXQ,cAAc,CAACF,EAAE,CAAC;IACpB,CAAC;EACH,CAAC,EAAE,CAACN,MAAM,CAAC,CAAC;EAEZ,oBAAOH,KAAA,CAAAY,aAAA,CAACX,MAAM;IAACE,MAAM,EAAEA,MAAM,GAAGE,WAAW,GAAG;EAAM,GAAED,QAAiB,CAAC;AAC1E;AAEA,eAAeF,aAAa", "ignoreList": []}