{"version": 3, "names": ["controlEdgeToEdgeValues", "isEdgeToEdge", "EDGE_TO_EDGE", "transformEdgeToEdgeProps", "props", "statusBarColor", "statusBarTranslucent", "navigationBarColor", "navigationBarTranslucent", "rest", "__DEV__"], "sourceRoot": "../../../../src", "sources": ["components/helpers/edge-to-edge.tsx"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,YAAY,QACP,8BAA8B;AAGrC,OAAO,MAAMC,YAAY,GAAGD,YAAY,CAAC,CAAC;AAE1C,OAAO,SAASE,wBAAwBA,CAACC,KAAkB,EAAe;EACxE,MAAM;IACJ;IACAC,cAAc;IACdC,oBAAoB;IACpBC,kBAAkB;IAClBC,wBAAwB;IACxB,GAAGC;EACL,CAAC,GAAGL,KAAK;EAET,IAAIM,OAAO,EAAE;IACXV,uBAAuB,CAAC;MACtBK,cAAc;MACdC,oBAAoB;MACpBC,kBAAkB;MAClBC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOC,IAAI;AACb", "ignoreList": []}