{"version": 3, "names": ["React", "Platform", "StyleSheet", "View", "useWindowDimensions", "FullWindowOverlayNativeComponent", "NativeFullWindowOverlay", "FullWindowOverlay", "props", "width", "height", "OS", "console", "warn", "createElement", "style", "absoluteFill", "accessibilityContainerViewIsModal", "unstable_accessibilityContainerViewIsModal", "children"], "sourceRoot": "../../../src", "sources": ["components/FullWindowOverlay.tsx"], "mappings": "AAAA,OAAOA,KAAK,MAAwC,OAAO;AAC3D,SACEC,QAAQ,EAERC,UAAU,EACVC,IAAI,EAEJC,mBAAmB,QACd,cAAc;;AAErB;AACA,OAAOC,gCAAgC,MAAM,4CAA4C;AAGzF,MAAMC,uBAKL,GAAGD,gCAAuC;AAO3C,SAASE,iBAAiBA,CAACC,KAA6B,EAAE;EACxD,MAAM;IAAEC,KAAK;IAAEC;EAAO,CAAC,GAAGN,mBAAmB,CAAC,CAAC;EAC/C,IAAIH,QAAQ,CAACU,EAAE,KAAK,KAAK,EAAE;IACzBC,OAAO,CAACC,IAAI,CAAC,uDAAuD,CAAC;IACrE,oBAAOb,KAAA,CAAAc,aAAA,CAACX,IAAI,EAAKK,KAAQ,CAAC;EAC5B;EACA,oBACER,KAAA,CAAAc,aAAA,CAACR,uBAAuB;IACtBS,KAAK,EAAE,CAACb,UAAU,CAACc,YAAY,EAAE;MAAEP,KAAK;MAAEC;IAAO,CAAC,CAAE;IACpDO,iCAAiC,EAC/BT,KAAK,CAACU;EACP,GACAV,KAAK,CAACW,QACgB,CAAC;AAE9B;AAEA,eAAeZ,iBAAiB", "ignoreList": []}