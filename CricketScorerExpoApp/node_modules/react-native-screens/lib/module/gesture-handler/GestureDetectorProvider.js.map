{"version": 3, "names": ["React", "GHContext", "ScreenGestureDetector", "GHWrapper", "props", "createElement", "GestureDetectorProvider", "Provider", "value", "children"], "sourceRoot": "../../../src", "sources": ["gesture-handler/GestureDetectorProvider.tsx"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,SAAS,QAAQ,aAAa;AACvC,OAAOC,qBAAqB,MAAM,yBAAyB;AAE3D,SAASC,SAASA,CAACC,KAA2B,EAAE;EAC9C,oBAAOJ,KAAA,CAAAK,aAAA,CAACH,qBAAqB,EAAKE,KAAQ,CAAC;AAC7C;AAEA,eAAe,SAASE,uBAAuBA,CAACF,KAE/C,EAAE;EACD,oBACEJ,KAAA,CAAAK,aAAA,CAACJ,SAAS,CAACM,QAAQ;IAACC,KAAK,EAAEL;EAAU,GAAEC,KAAK,CAACK,QAA6B,CAAC;AAE/E", "ignoreList": []}