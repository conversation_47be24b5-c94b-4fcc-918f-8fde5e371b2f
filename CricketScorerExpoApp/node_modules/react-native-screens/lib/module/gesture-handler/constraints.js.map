{"version": 3, "names": ["ScreenTransition", "AnimationForGesture", "SupportedGestures", "getAnimationForTransition", "goBackGesture", "customTransitionAnimation", "transitionAnimation", "SwipeRight", "Error", "includes", "undefined", "checkBoundaries", "event", "translationX", "translationY", "checkIfTransitionCancelled", "distanceX", "requiredXDistance", "distanceY", "requiredYDistance", "isTransitionCanceled", "Math", "abs", "isCanceledHorizontally", "isCanceledVertically"], "sourceRoot": "../../../src", "sources": ["gesture-handler/constraints.ts"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,yBAAyB;AAM1D,SAASC,mBAAmB,QAAQ,YAAY;AAGhD,MAAMC,iBAAiB,GAAG,CACxB,YAAY,EACZ,WAAW,EACX,WAAW,EACX,SAAS,EACT,iBAAiB,EACjB,eAAe,EACf,qBAAqB,CACtB;AAED,OAAO,SAASC,yBAAyBA,CACvCC,aAAwC,EACxCC,yBAA+D,EAC/D;EACA,IAAIC,mBAAmB,GAAGN,gBAAgB,CAACO,UAAU;EACrD,IAAIF,yBAAyB,EAAE;IAC7BC,mBAAmB,GAAGD,yBAAyB;IAC/C,IAAI,CAACD,aAAa,EAAE;MAClB,MAAM,IAAII,KAAK,CACb,mFACF,CAAC;IACH;EACF,CAAC,MAAM;IACL,IAAI,CAAC,CAACJ,aAAa,IAAIF,iBAAiB,CAACO,QAAQ,CAACL,aAAa,CAAC,EAAE;MAChEE,mBAAmB,GAAGL,mBAAmB,CAACG,aAAa,CAAC;IAC1D,CAAC,MAAM,IAAIA,aAAa,KAAKM,SAAS,EAAE;MACtC,MAAM,IAAIF,KAAK,CACb,mEAAmEJ,aAAa,GAClF,CAAC;IACH;EACF;EACA,OAAOE,mBAAmB;AAC5B;AAEA,OAAO,SAASK,eAAeA,CAC7BP,aAAiC,EACjCQ,KAAwD,EACxD;EACA,SAAS;;EACT,IAAIR,aAAa,KAAK,YAAY,IAAIQ,KAAK,CAACC,YAAY,GAAG,CAAC,EAAE;IAC5DD,KAAK,CAACC,YAAY,GAAG,CAAC;EACxB,CAAC,MAAM,IAAIT,aAAa,KAAK,WAAW,IAAIQ,KAAK,CAACC,YAAY,GAAG,CAAC,EAAE;IAClED,KAAK,CAACC,YAAY,GAAG,CAAC;EACxB,CAAC,MAAM,IAAIT,aAAa,KAAK,WAAW,IAAIQ,KAAK,CAACE,YAAY,GAAG,CAAC,EAAE;IAClEF,KAAK,CAACE,YAAY,GAAG,CAAC;EACxB,CAAC,MAAM,IAAIV,aAAa,KAAK,SAAS,IAAIQ,KAAK,CAACE,YAAY,GAAG,CAAC,EAAE;IAChEF,KAAK,CAACE,YAAY,GAAG,CAAC;EACxB;AACF;AAEA,OAAO,SAASC,0BAA0BA,CACxCX,aAAiC,EACjCY,SAAiB,EACjBC,iBAAyB,EACzBC,SAAiB,EACjBC,iBAAyB,EACzB;EACA,SAAS;;EACT,IAAIC,oBAAoB,GAAG,KAAK;EAChC,IAAIhB,aAAa,KAAK,YAAY,EAAE;IAClCgB,oBAAoB,GAAGJ,SAAS,GAAGC,iBAAiB;EACtD,CAAC,MAAM,IAAIb,aAAa,KAAK,WAAW,EAAE;IACxCgB,oBAAoB,GAAG,CAACJ,SAAS,GAAGC,iBAAiB;EACvD,CAAC,MAAM,IAAIb,aAAa,KAAK,iBAAiB,EAAE;IAC9CgB,oBAAoB,GAAGC,IAAI,CAACC,GAAG,CAACN,SAAS,CAAC,GAAGC,iBAAiB;EAChE,CAAC,MAAM,IAAIb,aAAa,KAAK,SAAS,EAAE;IACtCgB,oBAAoB,GAAG,CAACF,SAAS,GAAGC,iBAAiB;EACvD,CAAC,MAAM,IAAIf,aAAa,KAAK,WAAW,EAAE;IACxCgB,oBAAoB,GAAGF,SAAS,GAAGC,iBAAiB;EACtD,CAAC,MAAM,IAAIf,aAAa,KAAK,eAAe,EAAE;IAC5CgB,oBAAoB,GAAGC,IAAI,CAACC,GAAG,CAACJ,SAAS,CAAC,GAAGC,iBAAiB;EAChE,CAAC,MAAM,IAAIf,aAAa,KAAK,qBAAqB,EAAE;IAClD,MAAMmB,sBAAsB,GAAGF,IAAI,CAACC,GAAG,CAACN,SAAS,CAAC,GAAGC,iBAAiB;IACtE,MAAMO,oBAAoB,GAAGH,IAAI,CAACC,GAAG,CAACJ,SAAS,CAAC,GAAGC,iBAAiB;IACpEC,oBAAoB,GAAGG,sBAAsB,IAAIC,oBAAoB;EACvE;EACA,OAAOJ,oBAAoB;AAC7B", "ignoreList": []}