/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 * @format
 */

'use strict';

import AnimatedValue from './AnimatedValue';
import AnimatedWithChildren from './AnimatedWithChildren';
import invariant from 'fbjs/lib/invariant';
type ValueXYListenerCallback = (value: {
  x: number,
  y: number,
  ...
}) => mixed;
let _uniqueId = 1;

/**
 * 2D Value for driving 2D animations, such as pan gestures. Almost identical
 * API to normal `Animated.Value`, but multiplexed.
 *
 * See https://reactnative.dev/docs/animatedvaluexy.html
 */
declare class AnimatedValueXY extends AnimatedWithChildren {
  x: AnimatedValue,
  y: AnimatedValue,
  _listeners: {
    [key: string]: {
      x: string,
      y: string,
      ...
    },
    ...
  },
  constructor(valueIn?: ?{
    +x: number | AnimatedValue,
    +y: number | AnimatedValue,
    ...
  }): any,
  setValue(value: {
    x: number,
    y: number,
    ...
  }): any,
  setOffset(offset: {
    x: number,
    y: number,
    ...
  }): any,
  flattenOffset(): void,
  extractOffset(): void,
  __getValue(): {
    x: number,
    y: number,
    ...
  },
  resetAnimation(callback?: (value: {
    x: number,
    y: number,
    ...
  }) => void): void,
  stopAnimation(callback?: (value: {
    x: number,
    y: number,
    ...
  }) => void): void,
  addListener(callback: ValueXYListenerCallback): string,
  removeListener(id: string): void,
  removeAllListeners(): void,
  getLayout(): {
    [key: string]: AnimatedValue,
    ...
  },
  getTranslateTransform(): Array<{
    [key: string]: AnimatedValue,
    ...
  }>,
}
export default AnimatedValueXY;