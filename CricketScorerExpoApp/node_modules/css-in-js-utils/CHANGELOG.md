# Changelog

### 3.0.2
* added new unitless properties

### 3.0.0
* `assignStyle` now correctly merges array values without duplicates where the last occurence always wins in order

### 2.0.0
* improve `assignStyle` to replace arrays

### 1.0.3
* performance improvements

### 1.0.2
* added `resolveArrayValue` and `assignStyle`

### 1.0.1
* added `cssifyDeclaration` and `cssifyObject`

### 1.0.0
Initial version
