import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  useColorScheme,
  Animated,
  Easing,
} from 'react-native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import type { RouteProp } from '@react-navigation/native';

type RootStackParamList = {
  CoinFlipAnimation: {
    overs: string;
    playersPerTeam: string;
    team1Name: string;
    team2Name: string;
    team1Players: string[];
    team2Players: string[];
  };
  TossDecision: {
    overs: string;
    playersPerTeam: string;
    team1Name: string;
    team2Name: string;
    team1Players: string[];
    team2Players: string[];
    tossWinner: string;
  };
};

type CoinFlipAnimationScreenProps = {
  navigation: NativeStackNavigationProp<RootStackParamList, 'CoinFlipAnimation'>;
  route: RouteProp<RootStackParamList, 'CoinFlipAnimation'>;
};

const CoinFlipAnimationScreen: React.FC<CoinFlipAnimationScreenProps> = ({ navigation, route }) => {
  const isDarkMode = useColorScheme() === 'dark';
  const { overs, playersPerTeam, team1Name, team2Name, team1Players, team2Players } = route.params;
  
  const [flipResult, setFlipResult] = useState<string | null>(null);
  const [tossWinner, setTossWinner] = useState<string | null>(null);
  const [animationComplete, setAnimationComplete] = useState(false);
  
  // Animation values
  const flipAnimation = new Animated.Value(0);
  const opacityAnimation = new Animated.Value(1);
  
  const backgroundStyle = {
    backgroundColor: isDarkMode ? '#121212' : '#F5F5F5',
  };
  
  const textColor = {
    color: isDarkMode ? '#FFFFFF' : '#000000',
  };

  // Interpolate rotation for 3D flip effect
  const flipInterpolation = flipAnimation.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: ['0deg', '180deg', '360deg'],
  });

  // Determine which side of coin is showing
  const coinSide = flipAnimation.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: ['Heads', 'Tails', 'Heads'],
  });

  useEffect(() => {
    // Start the animation sequence
    startCoinFlip();
  }, []);

  const startCoinFlip = () => {
    // Reset state
    setFlipResult(null);
    setTossWinner(null);
    setAnimationComplete(false);
    
    // Determine random result
    const result = Math.random() < 0.5 ? 'Heads' : 'Tails';
    
    // Animate the coin flip
    Animated.sequence([
      // Reset opacity
      Animated.timing(opacityAnimation, {
        toValue: 1,
        duration: 0,
        useNativeDriver: true,
      }),
      
      // Multiple fast flips
      Animated.timing(flipAnimation, {
        toValue: 6, // 6 complete rotations
        duration: 2000,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }),
    ]).start(() => {
      // Set the result after animation completes
      setFlipResult(result);
      
      // Determine toss winner based on result
      // For simplicity, we'll say team1 chose Heads, team2 chose Tails
      const winner = result === 'Heads' ? team1Name : team2Name;
      setTossWinner(winner);
      setAnimationComplete(true);
    });
  };

  const handleContinue = () => {
    if (tossWinner) {
      navigation.navigate('TossDecision', {
        overs,
        playersPerTeam,
        team1Name,
        team2Name,
        team1Players,
        team2Players,
        tossWinner,
      });
    }
  };

  return (
    <SafeAreaView style={[styles.container, backgroundStyle]}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={backgroundStyle.backgroundColor}
      />
      <View style={styles.content}>
        <Text style={[styles.title, textColor]}>Coin Toss</Text>
        
        <View style={styles.coinContainer}>
          <Animated.View
            style={[
              styles.coin,
              {
                opacity: opacityAnimation,
                transform: [{ rotateY: flipInterpolation }],
                backgroundColor: flipAnimation.interpolate({
                  inputRange: [0, 0.5, 1],
                  outputRange: ['#FFD700', '#C0C0C0', '#FFD700'],
                }),
              },
            ]}
          >
            <Animated.Text style={styles.coinText}>
              {coinSide}
            </Animated.Text>
          </Animated.View>
        </View>
        
        {flipResult && (
          <View style={styles.resultContainer}>
            <Text style={[styles.resultText, textColor]}>
              It's {flipResult}!
            </Text>
            <Text style={[styles.winnerText, textColor]}>
              {tossWinner} won the toss!
            </Text>
          </View>
        )}
        
        {!animationComplete ? (
          <Text style={[styles.waitText, textColor]}>
            Flipping coin...
          </Text>
        ) : (
          <TouchableOpacity
            style={styles.button}
            onPress={handleContinue}
          >
            <Text style={styles.buttonText}>Continue</Text>
          </TouchableOpacity>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 40,
    textAlign: 'center',
  },
  coinContainer: {
    height: 200,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 40,
  },
  coin: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: '#FFD700', // Gold color
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.5,
    shadowRadius: 5,
  },
  coinText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  resultContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  resultText: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  winnerText: {
    fontSize: 22,
    fontWeight: '500',
  },
  waitText: {
    fontSize: 18,
    opacity: 0.7,
  },
  button: {
    backgroundColor: '#4CAF50',
    paddingVertical: 15,
    paddingHorizontal: 40,
    borderRadius: 30,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default CoinFlipAnimationScreen;
