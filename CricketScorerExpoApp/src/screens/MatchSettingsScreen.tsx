import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  useColorScheme,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';

type RootStackParamList = {
  Start: undefined;
  MatchSettings: undefined;
  TeamPlayers: {
    overs: string;
    playersPerTeam: string;
  };
};

type MatchSettingsScreenProps = {
  navigation: NativeStackNavigationProp<RootStackParamList, 'MatchSettings'>;
};

const MatchSettingsScreen: React.FC<MatchSettingsScreenProps> = ({ navigation }) => {
  const isDarkMode = useColorScheme() === 'dark';
  const [overs, setOvers] = React.useState('');
  const [playersPerTeam, setPlayersPerTeam] = React.useState('');
  const [error, setError] = React.useState('');
  
  const backgroundStyle = {
    backgroundColor: isDarkMode ? '#121212' : '#F5F5F5',
  };
  
  const textColor = {
    color: isDarkMode ? '#FFFFFF' : '#000000',
  };

  const handleContinue = () => {
    if (!overs || !playersPerTeam) {
      setError('Please enter both overs and players per team');
      return;
    }
    
    const oversNum = parseInt(overs, 10);
    const playersNum = parseInt(playersPerTeam, 10);
    
    if (isNaN(oversNum) || oversNum <= 0) {
      setError('Please enter a valid number of overs');
      return;
    }
    
    if (isNaN(playersNum) || playersNum < 2 || playersNum > 11) {
      setError('Please enter a valid number of players (2-11)');
      return;
    }
    
    setError('');
    navigation.navigate('TeamPlayers', {
      overs,
      playersPerTeam,
    });
  };

  return (
    <SafeAreaView style={[styles.container, backgroundStyle]}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={backgroundStyle.backgroundColor}
      />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoid}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.content}>
            <Text style={[styles.title, textColor]}>Match Settings</Text>
            
            <View style={styles.inputContainer}>
              <Text style={[styles.label, textColor]}>Number of Overs</Text>
              <TextInput
                style={[styles.input, textColor, { borderColor: isDarkMode ? '#555' : '#ccc' }]}
                placeholder="Enter overs (e.g. 20)"
                placeholderTextColor={isDarkMode ? '#888' : '#999'}
                keyboardType="numeric"
                value={overs}
                onChangeText={setOvers}
              />
            </View>
            
            <View style={styles.inputContainer}>
              <Text style={[styles.label, textColor]}>Players per Team</Text>
              <TextInput
                style={[styles.input, textColor, { borderColor: isDarkMode ? '#555' : '#ccc' }]}
                placeholder="Enter players (2-11)"
                placeholderTextColor={isDarkMode ? '#888' : '#999'}
                keyboardType="numeric"
                value={playersPerTeam}
                onChangeText={setPlayersPerTeam}
              />
            </View>
            
            {error ? <Text style={styles.errorText}>{error}</Text> : null}
            
            <TouchableOpacity
              style={styles.button}
              onPress={handleContinue}
            >
              <Text style={styles.buttonText}>Continue</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 30,
    textAlign: 'center',
  },
  inputContainer: {
    width: '100%',
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: '500',
  },
  input: {
    width: '100%',
    height: 50,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 15,
    fontSize: 16,
  },
  errorText: {
    color: '#FF6B6B',
    marginBottom: 20,
    textAlign: 'center',
  },
  button: {
    backgroundColor: '#4CAF50',
    paddingVertical: 15,
    paddingHorizontal: 40,
    borderRadius: 30,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    marginTop: 20,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default MatchSettingsScreen;
