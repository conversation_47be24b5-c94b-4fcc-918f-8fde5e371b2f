import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  useColorScheme,
  Image,
} from 'react-native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import type { RouteProp } from '@react-navigation/native';

type RootStackParamList = {
  TeamPlayers: {
    overs: string;
    playersPerTeam: string;
  };
  TossScreen: {
    overs: string;
    playersPerTeam: string;
    team1Name: string;
    team2Name: string;
    team1Players: string[];
    team2Players: string[];
  };
  TossResult: {
    overs: string;
    playersPerTeam: string;
    team1Name: string;
    team2Name: string;
    team1Players: string[];
    team2Players: string[];
    tossWinner: string;
    tossDecision: string;
  };
};

type TossScreenProps = {
  navigation: NativeStackNavigationProp<RootStackParamList, 'TossScreen'>;
  route: RouteProp<RootStackParamList, 'TossScreen'>;
};

const TossScreen: React.FC<TossScreenProps> = ({ navigation, route }) => {
  const isDarkMode = useColorScheme() === 'dark';
  const { overs, playersPerTeam, team1Name, team2Name, team1Players, team2Players } = route.params;
  
  const backgroundStyle = {
    backgroundColor: isDarkMode ? '#121212' : '#F5F5F5',
  };
  
  const textColor = {
    color: isDarkMode ? '#FFFFFF' : '#000000',
  };

  const handleOfflineToss = () => {
    // Navigate to offline toss selection
    navigation.navigate('OfflineTossResult', {
      overs,
      playersPerTeam,
      team1Name,
      team2Name,
      team1Players,
      team2Players,
    });
  };

  const handleCoinFlip = () => {
    // Navigate to coin flip animation
    navigation.navigate('CoinFlipAnimation', {
      overs,
      playersPerTeam,
      team1Name,
      team2Name,
      team1Players,
      team2Players,
    });
  };

  return (
    <SafeAreaView style={[styles.container, backgroundStyle]}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={backgroundStyle.backgroundColor}
      />
      <View style={styles.content}>
        <Text style={[styles.title, textColor]}>Toss Time</Text>
        <Text style={[styles.subtitle, textColor]}>
          Choose how you want to decide the toss
        </Text>
        
        <View style={styles.optionsContainer}>
          <TouchableOpacity
            style={[styles.optionButton, styles.offlineButton]}
            onPress={handleOfflineToss}
          >
            <Text style={styles.optionButtonText}>Offline Toss</Text>
            <Text style={styles.optionDescription}>
              Enter the toss result manually
            </Text>
          </TouchableOpacity>
          
          <Text style={[styles.orText, textColor]}>OR</Text>
          
          <TouchableOpacity
            style={[styles.optionButton, styles.coinFlipButton]}
            onPress={handleCoinFlip}
          >
            <Text style={styles.optionButtonText}>Flip Coin</Text>
            <Text style={styles.optionDescription}>
              Watch a virtual coin toss animation
            </Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.teamsContainer}>
          <Text style={[styles.teamsTitle, textColor]}>Match</Text>
          <View style={styles.teamVsTeam}>
            <Text style={[styles.teamName, textColor]}>{team1Name}</Text>
            <Text style={[styles.vsText, textColor]}>vs</Text>
            <Text style={[styles.teamName, textColor]}>{team2Name}</Text>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    marginBottom: 40,
    textAlign: 'center',
    opacity: 0.8,
  },
  optionsContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 40,
  },
  optionButton: {
    width: '90%',
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    marginBottom: 15,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  offlineButton: {
    backgroundColor: '#3498db',
  },
  coinFlipButton: {
    backgroundColor: '#f39c12',
  },
  optionButtonText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  optionDescription: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
  },
  orText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginVertical: 10,
  },
  teamsContainer: {
    width: '90%',
    padding: 15,
    borderRadius: 10,
    backgroundColor: 'rgba(0,0,0,0.05)',
    alignItems: 'center',
  },
  teamsTitle: {
    fontSize: 16,
    marginBottom: 10,
    fontWeight: '500',
  },
  teamVsTeam: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  teamName: {
    fontSize: 18,
    fontWeight: 'bold',
    paddingHorizontal: 10,
  },
  vsText: {
    fontSize: 16,
    opacity: 0.7,
  },
});

export default TossScreen;
