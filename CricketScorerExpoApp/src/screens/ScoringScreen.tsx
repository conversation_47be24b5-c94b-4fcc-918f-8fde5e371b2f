import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  useColorScheme,
  ScrollView,
  Alert,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import type { RouteProp } from '@react-navigation/native';

type RootStackParamList = {
  ScoringScreen: {
    overs: string;
    playersPerTeam: string;
    team1Name: string;
    team2Name: string;
    team1Players: string[];
    team2Players: string[];
    tossWinner: string;
    tossDecision: string;
    battingTeam: string;
    bowlingTeam: string;
    battingPlayers: string[];
    bowlingPlayers: string[];
  };
};

type ScoringScreenProps = {
  navigation: NativeStackNavigationProp<RootStackParamList, 'ScoringScreen'>;
  route: RouteProp<RootStackParamList, 'ScoringScreen'>;
};

const ScoringScreen: React.FC<ScoringScreenProps> = ({ navigation, route }) => {
  const isDarkMode = useColorScheme() === 'dark';
  const {
    overs,
    battingTeam,
    bowlingTeam,
    battingPlayers,
    bowlingPlayers,
  } = route.params;
  
  // State for player selection
  const [striker, setStriker] = useState<string>('');
  const [nonStriker, setNonStriker] = useState<string>('');
  const [bowler, setBowler] = useState<string>('');
  
  // Match state
  const [runs, setRuns] = useState<number>(0);
  const [wickets, setWickets] = useState<number>(0);
  const [currentOver, setCurrentOver] = useState<number>(0);
  const [ballsInOver, setBallsInOver] = useState<number>(0);
  const [overHistory, setOverHistory] = useState<string[]>([]);
  
  const backgroundStyle = {
    backgroundColor: isDarkMode ? '#121212' : '#F5F5F5',
  };
  
  const textColor = {
    color: isDarkMode ? '#FFFFFF' : '#000000',
  };

  // Validate player selection before allowing scoring
  const validatePlayerSelection = () => {
    if (!striker) {
      Alert.alert('Selection Required', 'Please select a striker');
      return false;
    }
    if (!nonStriker) {
      Alert.alert('Selection Required', 'Please select a non-striker');
      return false;
    }
    if (!bowler) {
      Alert.alert('Selection Required', 'Please select a bowler');
      return false;
    }
    if (striker === nonStriker) {
      Alert.alert('Invalid Selection', 'Striker and non-striker must be different players');
      return false;
    }
    return true;
  };

  // Handle run scoring
  const handleScore = (score: number | string) => {
    if (!validatePlayerSelection()) {
      return;
    }
    
    let scoreValue = 0;
    let ballCounted = true;
    let currentBallHistory = '';
    
    // Process different types of scoring
    if (typeof score === 'number') {
      // Regular runs
      scoreValue = score;
      currentBallHistory = score.toString();
      setRuns(runs + score);
      
      // Swap batsmen if odd number of runs
      if (score % 2 === 1) {
        const temp = striker;
        setStriker(nonStriker);
        setNonStriker(temp);
      }
    } else if (score === 'W') {
      // Wicket
      currentBallHistory = 'W';
      setWickets(wickets + 1);
      
      // Reset striker (would need UI to select new batsman in a real app)
      setStriker('');
      
      if (wickets + 1 >= battingPlayers.length - 1) {
        Alert.alert('Innings Over', 'All out!');
        // Would handle innings change here in a real app
      }
    } else if (score === 'LB' || score === 'B') {
      // Leg bye or bye
      const extraRuns = 1; // Simplified, would need UI to select number of runs
      scoreValue = extraRuns;
      currentBallHistory = score === 'LB' ? 'LB' : 'B';
      setRuns(runs + extraRuns);
      
      // Swap batsmen for a single
      const temp = striker;
      setStriker(nonStriker);
      setNonStriker(temp);
    }
    
    // Update over information
    if (ballCounted) {
      const newBallsInOver = ballsInOver + 1;
      setBallsInOver(newBallsInOver);
      
      // Update over history
      const updatedHistory = [...overHistory];
      if (!updatedHistory[currentOver]) {
        updatedHistory[currentOver] = '';
      }
      updatedHistory[currentOver] += currentBallHistory + ' ';
      setOverHistory(updatedHistory);
      
      // Check if over is complete
      if (newBallsInOver >= 6) {
        // End of over
        setBallsInOver(0);
        setCurrentOver(currentOver + 1);
        
        // No batsmen swap at end of over for gully cricket
        
        // Reset bowler (would need UI to select new bowler in a real app)
        setBowler('');
        
        // Check if innings is complete
        if (currentOver + 1 >= parseInt(overs, 10)) {
          Alert.alert('Innings Over', 'Maximum overs reached!');
          // Would handle innings change here in a real app
        }
      }
    }
  };

  return (
    <SafeAreaView style={[styles.container, backgroundStyle]}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={backgroundStyle.backgroundColor}
      />
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.scoreboardContainer}>
          <Text style={[styles.teamName, textColor]}>{battingTeam}</Text>
          <Text style={[styles.scoreText, textColor]}>
            {runs}/{wickets}
          </Text>
          <Text style={[styles.oversText, textColor]}>
            {currentOver}.{ballsInOver} overs
          </Text>
        </View>
        
        <View style={styles.playerSelectionContainer}>
          <Text style={[styles.sectionTitle, textColor]}>Player Selection</Text>
          
          <View style={styles.pickerContainer}>
            <Text style={[styles.pickerLabel, textColor]}>Striker</Text>
            <View style={[styles.picker, { borderColor: isDarkMode ? '#555' : '#ccc' }]}>
              <Picker
                selectedValue={striker}
                onValueChange={(itemValue) => setStriker(itemValue)}
                style={{ color: isDarkMode ? '#FFFFFF' : '#000000' }}
              >
                <Picker.Item label="Select Striker" value="" />
                {battingPlayers.map((player, index) => (
                  <Picker.Item key={`striker-${index}`} label={player} value={player} />
                ))}
              </Picker>
            </View>
          </View>
          
          <View style={styles.pickerContainer}>
            <Text style={[styles.pickerLabel, textColor]}>Non-Striker</Text>
            <View style={[styles.picker, { borderColor: isDarkMode ? '#555' : '#ccc' }]}>
              <Picker
                selectedValue={nonStriker}
                onValueChange={(itemValue) => setNonStriker(itemValue)}
                style={{ color: isDarkMode ? '#FFFFFF' : '#000000' }}
              >
                <Picker.Item label="Select Non-Striker" value="" />
                {battingPlayers.map((player, index) => (
                  <Picker.Item key={`non-striker-${index}`} label={player} value={player} />
                ))}
              </Picker>
            </View>
          </View>
          
          <View style={styles.pickerContainer}>
            <Text style={[styles.pickerLabel, textColor]}>Bowler</Text>
            <View style={[styles.picker, { borderColor: isDarkMode ? '#555' : '#ccc' }]}>
              <Picker
                selectedValue={bowler}
                onValueChange={(itemValue) => setBowler(itemValue)}
                style={{ color: isDarkMode ? '#FFFFFF' : '#000000' }}
              >
                <Picker.Item label="Select Bowler" value="" />
                {bowlingPlayers.map((player, index) => (
                  <Picker.Item key={`bowler-${index}`} label={player} value={player} />
                ))}
              </Picker>
            </View>
          </View>
        </View>
        
        <View style={styles.scoringContainer}>
          <Text style={[styles.sectionTitle, textColor]}>Scoring</Text>
          
          <View style={styles.runsButtonsContainer}>
            {[0, 1, 2, 3, 4, 5, 6].map((run) => (
              <TouchableOpacity
                key={`run-${run}`}
                style={styles.runButton}
                onPress={() => handleScore(run)}
              >
                <Text style={styles.runButtonText}>{run}</Text>
              </TouchableOpacity>
            ))}
          </View>
          
          <View style={styles.extraButtonsContainer}>
            <TouchableOpacity
              style={[styles.extraButton, styles.wicketButton]}
              onPress={() => handleScore('W')}
            >
              <Text style={styles.extraButtonText}>Wicket</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.extraButton, styles.byeButton]}
              onPress={() => handleScore('B')}
            >
              <Text style={styles.extraButtonText}>Bye</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.extraButton, styles.legByeButton]}
              onPress={() => handleScore('LB')}
            >
              <Text style={styles.extraButtonText}>Leg Bye</Text>
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.overHistoryContainer}>
          <Text style={[styles.sectionTitle, textColor]}>Current Over</Text>
          <Text style={[styles.overHistoryText, textColor]}>
            {overHistory[currentOver] || ''}
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  scoreboardContainer: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
    marginBottom: 20,
    alignItems: 'center',
  },
  teamName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  scoreText: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  oversText: {
    fontSize: 16,
  },
  playerSelectionContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  pickerContainer: {
    marginBottom: 12,
  },
  pickerLabel: {
    fontSize: 16,
    marginBottom: 6,
  },
  picker: {
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 8,
  },
  scoringContainer: {
    marginBottom: 20,
  },
  runsButtonsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  runButton: {
    width: '13%',
    aspectRatio: 1,
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  runButtonText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  extraButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  extraButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 4,
  },
  wicketButton: {
    backgroundColor: '#e74c3c',
  },
  byeButton: {
    backgroundColor: '#3498db',
  },
  legByeButton: {
    backgroundColor: '#f39c12',
  },
  extraButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  overHistoryContainer: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  overHistoryText: {
    fontSize: 18,
    letterSpacing: 2,
  },
});

export default ScoringScreen;
