import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  useColorScheme,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import type { RouteProp } from '@react-navigation/native';

type RootStackParamList = {
  Start: undefined;
  MatchSettings: undefined;
  TeamPlayers: {
    overs: string;
    playersPerTeam: string;
  };
  TossScreen: {
    overs: string;
    playersPerTeam: string;
    team1Name: string;
    team2Name: string;
    team1Players: string[];
    team2Players: string[];
  };
};

type TeamPlayersScreenProps = {
  navigation: NativeStackNavigationProp<RootStackParamList, 'TeamPlayers'>;
  route: RouteProp<RootStackParamList, 'TeamPlayers'>;
};

const TeamPlayersScreen: React.FC<TeamPlayersScreenProps> = ({ navigation, route }) => {
  const isDarkMode = useColorScheme() === 'dark';
  const { overs, playersPerTeam } = route.params;
  const numPlayers = parseInt(playersPerTeam, 10);
  
  const [team1Name, setTeam1Name] = useState('Team 1');
  const [team2Name, setTeam2Name] = useState('Team 2');
  const [team1Players, setTeam1Players] = useState<string[]>([]);
  const [team2Players, setTeam2Players] = useState<string[]>([]);
  
  // Initialize player arrays with empty strings
  useEffect(() => {
    setTeam1Players(Array(numPlayers).fill(''));
    setTeam2Players(Array(numPlayers).fill(''));
  }, [numPlayers]);
  
  const backgroundStyle = {
    backgroundColor: isDarkMode ? '#121212' : '#F5F5F5',
  };
  
  const textColor = {
    color: isDarkMode ? '#FFFFFF' : '#000000',
  };

  const updateTeam1Player = (index: number, name: string) => {
    const newPlayers = [...team1Players];
    newPlayers[index] = name;
    setTeam1Players(newPlayers);
  };

  const updateTeam2Player = (index: number, name: string) => {
    const newPlayers = [...team2Players];
    newPlayers[index] = name;
    setTeam2Players(newPlayers);
  };

  const handleContinue = () => {
    // Validate team names
    if (!team1Name.trim() || !team2Name.trim()) {
      Alert.alert('Error', 'Please enter names for both teams');
      return;
    }
    
    // Validate player names
    const emptyTeam1Players = team1Players.filter(name => !name.trim()).length;
    const emptyTeam2Players = team2Players.filter(name => !name.trim()).length;
    
    if (emptyTeam1Players > 0 || emptyTeam2Players > 0) {
      Alert.alert('Error', 'Please enter names for all players');
      return;
    }
    
    // Navigate to toss screen
    navigation.navigate('TossScreen', {
      overs,
      playersPerTeam,
      team1Name,
      team2Name,
      team1Players,
      team2Players,
    });
  };

  return (
    <SafeAreaView style={[styles.container, backgroundStyle]}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={backgroundStyle.backgroundColor}
      />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoid}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.content}>
            <Text style={[styles.title, textColor]}>Team Details</Text>
            
            {/* Team 1 Section */}
            <View style={styles.teamSection}>
              <Text style={[styles.teamTitle, textColor]}>Team 1</Text>
              <TextInput
                style={[styles.input, textColor, { borderColor: isDarkMode ? '#555' : '#ccc' }]}
                placeholder="Team 1 Name"
                placeholderTextColor={isDarkMode ? '#888' : '#999'}
                value={team1Name}
                onChangeText={setTeam1Name}
              />
              
              <Text style={[styles.playersTitle, textColor]}>Players</Text>
              {team1Players.map((player, index) => (
                <TextInput
                  key={`team1-player-${index}`}
                  style={[styles.input, textColor, { borderColor: isDarkMode ? '#555' : '#ccc', marginBottom: 10 }]}
                  placeholder={`Player ${index + 1}`}
                  placeholderTextColor={isDarkMode ? '#888' : '#999'}
                  value={player}
                  onChangeText={(text) => updateTeam1Player(index, text)}
                />
              ))}
            </View>
            
            {/* Team 2 Section */}
            <View style={styles.teamSection}>
              <Text style={[styles.teamTitle, textColor]}>Team 2</Text>
              <TextInput
                style={[styles.input, textColor, { borderColor: isDarkMode ? '#555' : '#ccc' }]}
                placeholder="Team 2 Name"
                placeholderTextColor={isDarkMode ? '#888' : '#999'}
                value={team2Name}
                onChangeText={setTeam2Name}
              />
              
              <Text style={[styles.playersTitle, textColor]}>Players</Text>
              {team2Players.map((player, index) => (
                <TextInput
                  key={`team2-player-${index}`}
                  style={[styles.input, textColor, { borderColor: isDarkMode ? '#555' : '#ccc', marginBottom: 10 }]}
                  placeholder={`Player ${index + 1}`}
                  placeholderTextColor={isDarkMode ? '#888' : '#999'}
                  value={player}
                  onChangeText={(text) => updateTeam2Player(index, text)}
                />
              ))}
            </View>
            
            <TouchableOpacity
              style={styles.button}
              onPress={handleContinue}
            >
              <Text style={styles.buttonText}>Continue to Toss</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 30,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  teamSection: {
    marginBottom: 30,
    padding: 15,
    borderRadius: 10,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  teamTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  playersTitle: {
    fontSize: 18,
    fontWeight: '500',
    marginTop: 15,
    marginBottom: 10,
  },
  input: {
    width: '100%',
    height: 50,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 15,
    fontSize: 16,
    marginBottom: 15,
  },
  button: {
    backgroundColor: '#4CAF50',
    paddingVertical: 15,
    paddingHorizontal: 40,
    borderRadius: 30,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    alignSelf: 'center',
    marginTop: 10,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default TeamPlayersScreen;
