{"name": "@react-native/eslint-plugin", "version": "0.79.2", "description": "ESLint rules for @react-native/eslint-config", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/facebook/react-native.git", "directory": "packages/eslint-plugin-react-native"}, "homepage": "https://github.com/facebook/react-native/tree/HEAD/packages/eslint-plugin-react-native#readme", "keywords": ["eslint", "rules", "eslint-config", "react-native"], "bugs": "https://github.com/facebook/react-native/issues", "main": "index.js", "devDependencies": {"babel-plugin-syntax-hermes-parser": "0.25.1", "hermes-eslint": "0.25.1"}, "engines": {"node": ">=18"}}