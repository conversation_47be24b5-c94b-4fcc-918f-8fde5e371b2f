{"name": "@react-native-community/cli-config-android", "version": "18.0.0", "license": "MIT", "main": "build/index.js", "publishConfig": {"access": "public"}, "dependencies": {"@react-native-community/cli-tools": "18.0.0", "chalk": "^4.1.2", "fast-glob": "^3.3.2", "fast-xml-parser": "^4.4.1"}, "files": ["build", "!*.d.ts", "!*.map", "native_modules.gradle"], "devDependencies": {"@react-native-community/cli-types": "18.0.0"}, "homepage": "https://github.com/react-native-community/cli/tree/main/packages/cli-config-android", "repository": {"type": "git", "url": "https://github.com/react-native-community/cli.git", "directory": "packages/cli-config-android"}, "gitHead": "f50c1f19a8068787d074560375b726d89c30a088"}