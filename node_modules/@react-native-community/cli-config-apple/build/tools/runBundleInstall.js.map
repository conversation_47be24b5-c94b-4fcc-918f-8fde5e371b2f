{"version": 3, "names": ["runBundleInstall", "loader", "start", "execa", "error", "fail", "logger", "stderr", "stdout", "CLIError", "link", "docs", "guide", "succeed"], "sources": ["../../src/tools/runBundleInstall.ts"], "sourcesContent": ["import execa from 'execa';\nimport {CLIError, logger, link} from '@react-native-community/cli-tools';\nimport type {Ora} from 'ora';\n\nasync function runBundleInstall(loader: Ora) {\n  try {\n    loader.start('Installing Ruby Gems');\n\n    await execa('bundle', ['install']);\n  } catch (error) {\n    loader.fail();\n    logger.error((error as any).stderr || (error as any).stdout);\n    throw new CLIError(\n      `Looks like your iOS environment is not properly set. Please go to ${link.docs(\n        'environment-setup',\n        'ios',\n        {guide: 'native'},\n      )} and follow the React Native CLI QuickStart guide for macOS and iOS.`,\n    );\n  }\n\n  loader.succeed();\n}\n\nexport default runBundleInstall;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAyE;AAGzE,eAAeA,gBAAgB,CAACC,MAAW,EAAE;EAC3C,IAAI;IACFA,MAAM,CAACC,KAAK,CAAC,sBAAsB,CAAC;IAEpC,MAAM,IAAAC,gBAAK,EAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,CAAC;EACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdH,MAAM,CAACI,IAAI,EAAE;IACbC,kBAAM,CAACF,KAAK,CAAEA,KAAK,CAASG,MAAM,IAAKH,KAAK,CAASI,MAAM,CAAC;IAC5D,MAAM,KAAIC,oBAAQ,EACf,qEAAoEC,gBAAI,CAACC,IAAI,CAC5E,mBAAmB,EACnB,KAAK,EACL;MAACC,KAAK,EAAE;IAAQ,CAAC,CACjB,sEAAqE,CACxE;EACH;EAEAX,MAAM,CAACY,OAAO,EAAE;AAClB;AAAC,eAEcb,gBAAgB;AAAA"}