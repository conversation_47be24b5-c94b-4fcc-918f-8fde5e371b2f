{"version": 3, "names": ["GLOB_EXCLUDE_PATTERN", "findAllPodfilePaths", "cwd", "glob", "sync", "unixifyPaths", "ignore", "deep"], "sources": ["../../src/config/findAllPodfilePaths.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nimport glob from 'fast-glob';\nimport {unixifyPaths} from '@react-native-community/cli-tools';\n\n// These folders will be excluded from search to speed it up\nconst GLOB_EXCLUDE_PATTERN = ['**/@(Pods|node_modules|Carthage|vendor)/**'];\n\nexport default function findAllPodfilePaths(cwd: string) {\n  return glob.sync('**/Podfile', {\n    cwd: unixifyPaths(cwd),\n    ignore: GLOB_EXCLUDE_PATTERN,\n    // Stop unbounded globbing and infinite loops for projects\n    // with deeply nested subdirectories. The most likely result\n    // is `ios/Podfile`, so this depth should be plenty:\n    deep: 10,\n  });\n}\n"], "mappings": ";;;;;;AAOA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA+D;AAR/D;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACA,MAAMA,oBAAoB,GAAG,CAAC,4CAA4C,CAAC;AAE5D,SAASC,mBAAmB,CAACC,GAAW,EAAE;EACvD,OAAOC,mBAAI,CAACC,IAAI,CAAC,YAAY,EAAE;IAC7BF,GAAG,EAAE,IAAAG,wBAAY,EAACH,GAAG,CAAC;IACtBI,MAAM,EAAEN,oBAAoB;IAC5B;IACA;IACA;IACAO,IAAI,EAAE;EACR,CAAC,CAAC;AACJ"}