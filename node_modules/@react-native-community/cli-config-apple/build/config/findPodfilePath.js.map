{"version": 3, "names": ["TEST_PROJECTS", "BUNDLE_VENDORED_PODFILE", "findPodfilePath", "cwd", "platformName", "podfiles", "findAllPodfilePaths", "filter", "project", "path", "dirname", "test", "indexOf", "sort", "supportedPlatformsArray", "Object", "values", "supportedPlatforms", "containsUnsupportedPodfiles", "every", "podfile", "includes", "split", "length", "logger", "warn", "inlineString", "join"], "sources": ["../../src/config/findPodfilePath.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {inlineString, logger} from '@react-native-community/cli-tools';\nimport path from 'path';\nimport findAllPodfilePaths from './findAllPodfilePaths';\nimport {ApplePlatform} from '../types';\nimport {supportedPlatforms} from './supportedPlatforms';\n\n// Regexp matching all test projects\nconst TEST_PROJECTS = /test|example|sample/i;\n\n// Podfile in the bundle package\nconst BUNDLE_VENDORED_PODFILE = 'vendor/bundle/ruby';\n\nexport default function findPodfilePath(\n  cwd: string,\n  platformName: ApplePlatform,\n) {\n  const podfiles = findAllPodfilePaths(cwd)\n    /**\n     * Then, we will run a simple test to rule out most example projects,\n     * unless they are located in a `platformName` folder\n     */\n    .filter((project) => {\n      if (path.dirname(project) === platformName) {\n        // Pick the Podfile in the default project (in the iOS folder)\n        return true;\n      }\n\n      if (TEST_PROJECTS.test(project)) {\n        // Ignore the Podfile in test and example projects\n        return false;\n      }\n\n      if (project.indexOf(BUNDLE_VENDORED_PODFILE) > -1) {\n        // Ignore the podfile shipped with Cocoapods in bundle\n        return false;\n      }\n\n      // Accept all the others\n      return true;\n    })\n    /**\n     * Podfile from `platformName` folder will be picked up as a first one.\n     */\n    .sort((project) => (path.dirname(project) === platformName ? -1 : 1));\n\n  const supportedPlatformsArray: string[] = Object.values(supportedPlatforms);\n  const containsUnsupportedPodfiles = podfiles.every(\n    (podfile) => !supportedPlatformsArray.includes(podfile.split('/')[0]),\n  );\n\n  if (podfiles.length > 0) {\n    if (podfiles.length > 1 && containsUnsupportedPodfiles) {\n      logger.warn(\n        inlineString(`\n          Multiple Podfiles were found: ${podfiles}. Choosing ${podfiles[0]} automatically.\n          If you would like to select a different one, you can configure it via \"project.${platformName}.sourceDir\".\n          You can learn more about it here: https://github.com/react-native-community/cli/blob/main/docs/configuration.md\n        `),\n      );\n    }\n    return path.join(cwd, podfiles[0]);\n  }\n\n  return null;\n}\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAEA;AAAwD;AAZxD;AACA;AACA;AACA;AACA;AACA;AACA;;AAQA;AACA,MAAMA,aAAa,GAAG,sBAAsB;;AAE5C;AACA,MAAMC,uBAAuB,GAAG,oBAAoB;AAErC,SAASC,eAAe,CACrCC,GAAW,EACXC,YAA2B,EAC3B;EACA,MAAMC,QAAQ,GAAG,IAAAC,4BAAmB,EAACH,GAAG;EACtC;AACJ;AACA;AACA,KAHI,CAICI,MAAM,CAAEC,OAAO,IAAK;IACnB,IAAIC,eAAI,CAACC,OAAO,CAACF,OAAO,CAAC,KAAKJ,YAAY,EAAE;MAC1C;MACA,OAAO,IAAI;IACb;IAEA,IAAIJ,aAAa,CAACW,IAAI,CAACH,OAAO,CAAC,EAAE;MAC/B;MACA,OAAO,KAAK;IACd;IAEA,IAAIA,OAAO,CAACI,OAAO,CAACX,uBAAuB,CAAC,GAAG,CAAC,CAAC,EAAE;MACjD;MACA,OAAO,KAAK;IACd;;IAEA;IACA,OAAO,IAAI;EACb,CAAC;EACD;AACJ;AACA,KAFI,CAGCY,IAAI,CAAEL,OAAO,IAAMC,eAAI,CAACC,OAAO,CAACF,OAAO,CAAC,KAAKJ,YAAY,GAAG,CAAC,CAAC,GAAG,CAAE,CAAC;EAEvE,MAAMU,uBAAiC,GAAGC,MAAM,CAACC,MAAM,CAACC,sCAAkB,CAAC;EAC3E,MAAMC,2BAA2B,GAAGb,QAAQ,CAACc,KAAK,CAC/CC,OAAO,IAAK,CAACN,uBAAuB,CAACO,QAAQ,CAACD,OAAO,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CACtE;EAED,IAAIjB,QAAQ,CAACkB,MAAM,GAAG,CAAC,EAAE;IACvB,IAAIlB,QAAQ,CAACkB,MAAM,GAAG,CAAC,IAAIL,2BAA2B,EAAE;MACtDM,kBAAM,CAACC,IAAI,CACT,IAAAC,wBAAY,EAAE;AACtB,0CAA0CrB,QAAS,cAAaA,QAAQ,CAAC,CAAC,CAAE;AAC5E,2FAA2FD,YAAa;AACxG;AACA,SAAS,CAAC,CACH;IACH;IACA,OAAOK,eAAI,CAACkB,IAAI,CAACxB,GAAG,EAAEE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpC;EAEA,OAAO,IAAI;AACb"}