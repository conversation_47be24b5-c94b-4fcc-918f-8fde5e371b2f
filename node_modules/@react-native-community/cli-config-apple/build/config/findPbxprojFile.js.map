{"version": 3, "names": ["findPbxprojFile", "projectInfo", "path", "join", "name", "replace"], "sources": ["../../src/config/findPbxprojFile.ts"], "sourcesContent": ["import {IOSProjectInfo} from '@react-native-community/cli-types';\nimport path from 'path';\n\nfunction findPbxprojFile(projectInfo: IOSProjectInfo): string {\n  return path.join(\n    projectInfo.path,\n    projectInfo.name.replace('.xcworkspace', '.xcodeproj'),\n    'project.pbxproj',\n  );\n}\n\nexport default findPbxprojFile;\n"], "mappings": ";;;;;;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AAExB,SAASA,eAAe,CAACC,WAA2B,EAAU;EAC5D,OAAOC,eAAI,CAACC,IAAI,CACdF,WAAW,CAACC,IAAI,EAChBD,WAAW,CAACG,IAAI,CAACC,OAAO,CAAC,cAAc,EAAE,YAAY,CAAC,EACtD,iBAAiB,CAClB;AACH;AAAC,eAEcL,eAAe;AAAA"}