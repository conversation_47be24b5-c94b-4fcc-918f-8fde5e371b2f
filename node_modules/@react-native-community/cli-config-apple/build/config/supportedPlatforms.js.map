{"version": 3, "names": ["supportedPlatforms", "ios", "macos", "visionos", "tvos"], "sources": ["../../src/config/supportedPlatforms.ts"], "sourcesContent": ["export const supportedPlatforms = {\n  ios: 'ios',\n  macos: 'macos',\n  visionos: 'visionos',\n  tvos: 'tvos',\n} as const;\n"], "mappings": ";;;;;;AAAO,MAAMA,kBAAkB,GAAG;EAChCC,GAAG,EAAE,KAAK;EACVC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE;AACR,CAAU;AAAC"}