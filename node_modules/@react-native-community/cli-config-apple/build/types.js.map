{"version": 3, "names": [], "sources": ["../src/types.ts"], "sourcesContent": ["import {supportedPlatforms} from './config/supportedPlatforms';\n\ntype ObjectValues<T> = T[keyof T];\n\nexport type ApplePlatform = ObjectValues<typeof supportedPlatforms>;\n\nexport interface BuilderCommand {\n  /**\n   * Lowercase name of the platform.\n   * Example: 'ios', 'visionos'\n   */\n  platformName: ApplePlatform;\n}\n"], "mappings": ""}