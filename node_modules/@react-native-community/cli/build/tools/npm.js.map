{"version": 3, "names": ["getNpmVersionIfAvailable", "npmVersion", "execSync", "stdio", "toString", "trim", "error", "isProjectUsingNpm", "cwd", "findUp", "sync", "getNpmRegistryUrl", "registryUrl", "npmResolveConcreteVersion", "packageName", "tagOrVersion", "url", "URL", "pathname", "resp", "fetch", "indexOf", "status", "Error", "json", "version", "Template", "constructor", "reactNativeVersion", "published", "Date", "isPreRelease", "includes", "minorVersion", "v", "semver", "parse", "major", "minor", "getTemplateVersion", "then", "rnToTemplate", "templateVersion", "pkg", "Object", "entries", "versions", "rnVersion", "scripts", "valid", "template", "time", "rnMinorVersion", "push", "sort", "a", "b", "getTime"], "sources": ["../../src/tools/npm.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {execSync} from 'child_process';\nimport findUp from 'find-up';\nimport semver from 'semver';\n\nexport function getNpmVersionIfAvailable() {\n  let npmVersion;\n  try {\n    // execSync returns a Buffer -> convert to string\n    npmVersion = (\n      execSync('npm --version', {\n        stdio: [0, 'pipe', 'ignore'],\n      }).toString() || ''\n    ).trim();\n\n    return npmVersion;\n  } catch (error) {\n    return null;\n  }\n}\n\nexport function isProjectUsingNpm(cwd: string) {\n  return findUp.sync('package-lock.json', {cwd});\n}\n\nexport const getNpmRegistryUrl = (() => {\n  // Lazily resolve npm registry url since it is only needed when initializing a\n  // new project.\n  let registryUrl = '';\n  return () => {\n    if (!registryUrl) {\n      try {\n        registryUrl = execSync(\n          'npm config get registry --workspaces=false --include-workspace-root',\n        )\n          .toString()\n          .trim();\n      } catch {\n        registryUrl = 'https://registry.npmjs.org/';\n      }\n    }\n    return registryUrl;\n  };\n})();\n\n/**\n * Convert an npm tag to a concrete version, for example:\n * - next -> 0.75.0-rc.0\n * - nightly -> 0.75.0-nightly-20240618-5df5ed1a8\n */\nexport async function npmResolveConcreteVersion(\n  packageName: string,\n  tagOrVersion: string,\n): Promise<string> {\n  const url = new URL(getNpmRegistryUrl());\n  url.pathname = `${packageName}/${tagOrVersion}`;\n  const resp = await fetch(url);\n  if (\n    [\n      200, // OK\n      301, // Moved Permanemently\n      302, // Found\n      304, // Not Modified\n      307, // Temporary Redirect\n      308, // Permanent Redirect\n    ].indexOf(resp.status) === -1\n  ) {\n    throw new Error(`Unknown version ${packageName}@${tagOrVersion}`);\n  }\n  const json: any = await resp.json();\n  return json.version;\n}\n\ntype TimeStampString = string;\ntype TemplateVersion = string;\ntype VersionedTemplates = {\n  [rnVersion: string]: Template[];\n};\n\ntype NpmTemplateResponse = {\n  versions: {\n    // Template version, semver including -rc candidates\n    [version: TemplateVersion]: {\n      scripts?: {\n        // Version of react-native this is built for\n        reactNativeVersion?: string;\n        // The initial implemntation used this, but moved to reactNativeVersion\n        version?: string;\n      };\n    };\n  };\n  time: {\n    created: string;\n    modified: string;\n    [version: TemplateVersion]: TimeStampString;\n  };\n};\n\nclass Template {\n  version: string;\n  reactNativeVersion: string;\n  published: Date;\n\n  constructor(version: string, reactNativeVersion: string, published: string) {\n    this.version = version;\n    this.reactNativeVersion = reactNativeVersion;\n    this.published = new Date(published);\n  }\n\n  get isPreRelease() {\n    return this.version.includes('-rc');\n  }\n}\n\nconst minorVersion = (version: string) => {\n  const v = semver.parse(version)!;\n  return `${v.major}.${v.minor}`;\n};\n\nexport async function getTemplateVersion(\n  reactNativeVersion: string,\n): Promise<TemplateVersion | undefined> {\n  const json = await fetch(\n    new URL('@react-native-community/template', getNpmRegistryUrl()),\n  ).then((resp) => resp.json() as Promise<NpmTemplateResponse>);\n\n  // We are abusing which npm metadata is publicly available through the registry. Scripts\n  // is always captured, and we use this in the Github Action that manages our releases to\n  // capture the version of React Native the template is built with.\n  //\n  // Users are interested in:\n  // - IF there a match for React Native MAJOR.MINOR.PATCH?\n  //    - Yes: if there are >= 2 versions, pick the one last published. This lets us release\n  //           specific fixes for React Native versions.\n  // - ELSE, is there a match for React Native MINOR.PATCH?\n  //    - Yes: if there are >= 2 versions, pick the one last published. This decouples us from\n  //           React Native releases.\n  //    - No: we don't have a version of the template for a version of React Native. There should\n  //          at a minimum be at last one version cut for each MINOR.PATCH since 0.75. Before this\n  //          the template was shipped with React Native\n  const rnToTemplate: VersionedTemplates = {};\n  for (const [templateVersion, pkg] of Object.entries(json.versions)) {\n    const rnVersion = pkg?.scripts?.reactNativeVersion ?? pkg?.scripts?.version;\n    if (rnVersion == null || !semver.valid(rnVersion)) {\n      // This is a very early version that doesn't have the correct metadata embedded\n      continue;\n    }\n\n    const template = new Template(\n      templateVersion,\n      rnVersion,\n      json.time[templateVersion],\n    );\n\n    const rnMinorVersion = minorVersion(rnVersion);\n\n    rnToTemplate[rnVersion] = rnToTemplate[rnVersion] ?? [];\n    rnToTemplate[rnVersion].push(template);\n    rnToTemplate[rnMinorVersion] = rnToTemplate[rnMinorVersion] ?? [];\n    rnToTemplate[rnMinorVersion].push(template);\n  }\n\n  // Make sure the last published is the first one in each version of React Native\n  for (const v in rnToTemplate) {\n    rnToTemplate[v].sort(\n      (a, b) => b.published.getTime() - a.published.getTime(),\n    );\n  }\n\n  if (reactNativeVersion in rnToTemplate) {\n    return rnToTemplate[reactNativeVersion][0].version;\n  }\n  const rnMinorVersion = minorVersion(reactNativeVersion);\n  if (rnMinorVersion in rnToTemplate) {\n    return rnToTemplate[rnMinorVersion][0].version;\n  }\n  return;\n}\n"], "mappings": ";;;;;;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA4B;AAV5B;AACA;AACA;AACA;AACA;AACA;AACA;;AAMO,SAASA,wBAAwB,GAAG;EACzC,IAAIC,UAAU;EACd,IAAI;IACF;IACAA,UAAU,GAAG,CACX,IAAAC,yBAAQ,EAAC,eAAe,EAAE;MACxBC,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,QAAQ;IAC7B,CAAC,CAAC,CAACC,QAAQ,EAAE,IAAI,EAAE,EACnBC,IAAI,EAAE;IAER,OAAOJ,UAAU;EACnB,CAAC,CAAC,OAAOK,KAAK,EAAE;IACd,OAAO,IAAI;EACb;AACF;AAEO,SAASC,iBAAiB,CAACC,GAAW,EAAE;EAC7C,OAAOC,iBAAM,CAACC,IAAI,CAAC,mBAAmB,EAAE;IAACF;EAAG,CAAC,CAAC;AAChD;AAEO,MAAMG,iBAAiB,GAAG,CAAC,MAAM;EACtC;EACA;EACA,IAAIC,WAAW,GAAG,EAAE;EACpB,OAAO,MAAM;IACX,IAAI,CAACA,WAAW,EAAE;MAChB,IAAI;QACFA,WAAW,GAAG,IAAAV,yBAAQ,EACpB,qEAAqE,CACtE,CACEE,QAAQ,EAAE,CACVC,IAAI,EAAE;MACX,CAAC,CAAC,MAAM;QACNO,WAAW,GAAG,6BAA6B;MAC7C;IACF;IACA,OAAOA,WAAW;EACpB,CAAC;AACH,CAAC,GAAG;;AAEJ;AACA;AACA;AACA;AACA;AAJA;AAKO,eAAeC,yBAAyB,CAC7CC,WAAmB,EACnBC,YAAoB,EACH;EACjB,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAACN,iBAAiB,EAAE,CAAC;EACxCK,GAAG,CAACE,QAAQ,GAAI,GAAEJ,WAAY,IAAGC,YAAa,EAAC;EAC/C,MAAMI,IAAI,GAAG,MAAMC,KAAK,CAACJ,GAAG,CAAC;EAC7B,IACE,CACE,GAAG;EAAE;EACL,GAAG;EAAE;EACL,GAAG;EAAE;EACL,GAAG;EAAE;EACL,GAAG;EAAE;EACL,GAAG,CAAE;EAAA,CACN,CAACK,OAAO,CAACF,IAAI,CAACG,MAAM,CAAC,KAAK,CAAC,CAAC,EAC7B;IACA,MAAM,IAAIC,KAAK,CAAE,mBAAkBT,WAAY,IAAGC,YAAa,EAAC,CAAC;EACnE;EACA,MAAMS,IAAS,GAAG,MAAML,IAAI,CAACK,IAAI,EAAE;EACnC,OAAOA,IAAI,CAACC,OAAO;AACrB;AA2BA,MAAMC,QAAQ,CAAC;EAKbC,WAAW,CAACF,OAAe,EAAEG,kBAA0B,EAAEC,SAAiB,EAAE;IAC1E,IAAI,CAACJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,SAAS,GAAG,IAAIC,IAAI,CAACD,SAAS,CAAC;EACtC;EAEA,IAAIE,YAAY,GAAG;IACjB,OAAO,IAAI,CAACN,OAAO,CAACO,QAAQ,CAAC,KAAK,CAAC;EACrC;AACF;AAEA,MAAMC,YAAY,GAAIR,OAAe,IAAK;EACxC,MAAMS,CAAC,GAAGC,iBAAM,CAACC,KAAK,CAACX,OAAO,CAAE;EAChC,OAAQ,GAAES,CAAC,CAACG,KAAM,IAAGH,CAAC,CAACI,KAAM,EAAC;AAChC,CAAC;AAEM,eAAeC,kBAAkB,CACtCX,kBAA0B,EACY;EACtC,MAAMJ,IAAI,GAAG,MAAMJ,KAAK,CACtB,IAAIH,GAAG,CAAC,kCAAkC,EAAEN,iBAAiB,EAAE,CAAC,CACjE,CAAC6B,IAAI,CAAErB,IAAI,IAAKA,IAAI,CAACK,IAAI,EAAkC,CAAC;;EAE7D;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMiB,YAAgC,GAAG,CAAC,CAAC;EAC3C,KAAK,MAAM,CAACC,eAAe,EAAEC,GAAG,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACrB,IAAI,CAACsB,QAAQ,CAAC,EAAE;IAAA;IAClE,MAAMC,SAAS,GAAG,CAAAJ,GAAG,aAAHA,GAAG,uCAAHA,GAAG,CAAEK,OAAO,iDAAZ,aAAcpB,kBAAkB,MAAIe,GAAG,aAAHA,GAAG,wCAAHA,GAAG,CAAEK,OAAO,kDAAZ,cAAcvB,OAAO;IAC3E,IAAIsB,SAAS,IAAI,IAAI,IAAI,CAACZ,iBAAM,CAACc,KAAK,CAACF,SAAS,CAAC,EAAE;MACjD;MACA;IACF;IAEA,MAAMG,QAAQ,GAAG,IAAIxB,QAAQ,CAC3BgB,eAAe,EACfK,SAAS,EACTvB,IAAI,CAAC2B,IAAI,CAACT,eAAe,CAAC,CAC3B;IAED,MAAMU,cAAc,GAAGnB,YAAY,CAACc,SAAS,CAAC;IAE9CN,YAAY,CAACM,SAAS,CAAC,GAAGN,YAAY,CAACM,SAAS,CAAC,IAAI,EAAE;IACvDN,YAAY,CAACM,SAAS,CAAC,CAACM,IAAI,CAACH,QAAQ,CAAC;IACtCT,YAAY,CAACW,cAAc,CAAC,GAAGX,YAAY,CAACW,cAAc,CAAC,IAAI,EAAE;IACjEX,YAAY,CAACW,cAAc,CAAC,CAACC,IAAI,CAACH,QAAQ,CAAC;EAC7C;;EAEA;EACA,KAAK,MAAMhB,CAAC,IAAIO,YAAY,EAAE;IAC5BA,YAAY,CAACP,CAAC,CAAC,CAACoB,IAAI,CAClB,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC3B,SAAS,CAAC4B,OAAO,EAAE,GAAGF,CAAC,CAAC1B,SAAS,CAAC4B,OAAO,EAAE,CACxD;EACH;EAEA,IAAI7B,kBAAkB,IAAIa,YAAY,EAAE;IACtC,OAAOA,YAAY,CAACb,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAACH,OAAO;EACpD;EACA,MAAM2B,cAAc,GAAGnB,YAAY,CAACL,kBAAkB,CAAC;EACvD,IAAIwB,cAAc,IAAIX,YAAY,EAAE;IAClC,OAAOA,YAAY,CAACW,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC3B,OAAO;EAChD;EACA;AACF"}