{"version": 3, "names": ["packageManagers", "yarn", "init", "install", "installDev", "uninstall", "installAll", "npm", "bun", "configurePackageManager", "packageNames", "action", "options", "yarnAvailable", "should<PERSON><PERSON><PERSON><PERSON><PERSON>", "bunAvailable", "shouldUseBun", "pm", "packageManager", "executable", "flags", "args", "executeCommand", "getYarnVersionIfAvailable", "isProjectUsingYarn", "root", "getBunVersionIfAvailable", "isProjectUsingBun", "shouldUseNpm", "getNpmVersionIfAvailable", "isProjectUsingNpm"], "sources": ["../../src/tools/packageManager.ts"], "sourcesContent": ["import {getYarnVersionIfAvailable, isProjectUsingYarn} from './yarn';\nimport {getBunVersionIfAvailable, isProjectUsingBun} from './bun';\nimport {getNpmVersionIfAvailable, isProjectUsingNpm} from './npm';\nimport {executeCommand} from './executeCommand';\n\nexport type PackageManager = keyof typeof packageManagers;\n\ntype Options = {\n  packageManager: PackageManager;\n  silent?: boolean;\n  root: string;\n};\n\nconst packageManagers = {\n  yarn: {\n    init: ['init', '-y'],\n    install: ['add'],\n    installDev: ['add', '-D'],\n    uninstall: ['remove'],\n    installAll: ['install'],\n  },\n  npm: {\n    init: ['init', '-y'],\n    install: ['install', '--save', '--save-exact'],\n    installDev: ['install', '--save-dev', '--save-exact'],\n    uninstall: ['uninstall', '--save'],\n    installAll: ['install'],\n  },\n  bun: {\n    init: ['init', '-y'],\n    install: ['add', '--exact'],\n    installDev: ['add', '--dev', '--exact'],\n    uninstall: ['remove'],\n    installAll: ['install'],\n  },\n};\n\nfunction configurePackageManager(\n  packageNames: Array<string>,\n  action: 'init' | 'install' | 'installDev' | 'installAll' | 'uninstall',\n  options: Options,\n) {\n  let yarnAvailable = shouldUseYarn(options);\n  let bunAvailable = shouldUseBun(options);\n\n  let pm: PackageManager = 'npm';\n\n  if (options.packageManager === 'bun') {\n    if (bunAvailable) {\n      pm = 'bun';\n    } else if (yarnAvailable) {\n      pm = 'yarn';\n    } else {\n      pm = 'npm';\n    }\n  }\n\n  if (options.packageManager === 'yarn' && yarnAvailable) {\n    pm = 'yarn';\n  }\n\n  const [executable, ...flags] = packageManagers[pm][action];\n  const args = [executable, ...flags, ...packageNames];\n  return executeCommand(pm, args, options);\n}\n\nexport function shouldUseYarn(options: Options) {\n  if (options.packageManager === 'yarn') {\n    return getYarnVersionIfAvailable();\n  }\n  return isProjectUsingYarn(options.root) && getYarnVersionIfAvailable();\n}\n\nexport function shouldUseBun(options: Options) {\n  if (options.packageManager === 'bun') {\n    return getBunVersionIfAvailable();\n  }\n\n  return isProjectUsingBun(options.root) && getBunVersionIfAvailable();\n}\n\nexport function shouldUseNpm(options: Options) {\n  if (options.packageManager === 'npm') {\n    return getNpmVersionIfAvailable();\n  }\n\n  return isProjectUsingNpm(options.root) && getNpmVersionIfAvailable();\n}\n\nexport function init(options: Options) {\n  return configurePackageManager([], 'init', options);\n}\n\nexport function install(packageNames: Array<string>, options: Options) {\n  return configurePackageManager(packageNames, 'install', options);\n}\n\nexport function installDev(packageNames: Array<string>, options: Options) {\n  return configurePackageManager(packageNames, 'installDev', options);\n}\n\nexport function uninstall(packageNames: Array<string>, options: Options) {\n  return configurePackageManager(packageNames, 'uninstall', options);\n}\n\nexport function installAll(options: Options) {\n  return configurePackageManager([], 'installAll', options);\n}\n"], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AAUA,MAAMA,eAAe,GAAG;EACtBC,IAAI,EAAE;IACJC,IAAI,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC;IACpBC,OAAO,EAAE,CAAC,KAAK,CAAC;IAChBC,UAAU,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;IACzBC,SAAS,EAAE,CAAC,QAAQ,CAAC;IACrBC,UAAU,EAAE,CAAC,SAAS;EACxB,CAAC;EACDC,GAAG,EAAE;IACHL,IAAI,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC;IACpBC,OAAO,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,cAAc,CAAC;IAC9CC,UAAU,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,cAAc,CAAC;IACrDC,SAAS,EAAE,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCC,UAAU,EAAE,CAAC,SAAS;EACxB,CAAC;EACDE,GAAG,EAAE;IACHN,IAAI,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC;IACpBC,OAAO,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC;IAC3BC,UAAU,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,CAAC;IACvCC,SAAS,EAAE,CAAC,QAAQ,CAAC;IACrBC,UAAU,EAAE,CAAC,SAAS;EACxB;AACF,CAAC;AAED,SAASG,uBAAuB,CAC9BC,YAA2B,EAC3BC,MAAsE,EACtEC,OAAgB,EAChB;EACA,IAAIC,aAAa,GAAGC,aAAa,CAACF,OAAO,CAAC;EAC1C,IAAIG,YAAY,GAAGC,YAAY,CAACJ,OAAO,CAAC;EAExC,IAAIK,EAAkB,GAAG,KAAK;EAE9B,IAAIL,OAAO,CAACM,cAAc,KAAK,KAAK,EAAE;IACpC,IAAIH,YAAY,EAAE;MAChBE,EAAE,GAAG,KAAK;IACZ,CAAC,MAAM,IAAIJ,aAAa,EAAE;MACxBI,EAAE,GAAG,MAAM;IACb,CAAC,MAAM;MACLA,EAAE,GAAG,KAAK;IACZ;EACF;EAEA,IAAIL,OAAO,CAACM,cAAc,KAAK,MAAM,IAAIL,aAAa,EAAE;IACtDI,EAAE,GAAG,MAAM;EACb;EAEA,MAAM,CAACE,UAAU,EAAE,GAAGC,KAAK,CAAC,GAAGpB,eAAe,CAACiB,EAAE,CAAC,CAACN,MAAM,CAAC;EAC1D,MAAMU,IAAI,GAAG,CAACF,UAAU,EAAE,GAAGC,KAAK,EAAE,GAAGV,YAAY,CAAC;EACpD,OAAO,IAAAY,8BAAc,EAACL,EAAE,EAAEI,IAAI,EAAET,OAAO,CAAC;AAC1C;AAEO,SAASE,aAAa,CAACF,OAAgB,EAAE;EAC9C,IAAIA,OAAO,CAACM,cAAc,KAAK,MAAM,EAAE;IACrC,OAAO,IAAAK,+BAAyB,GAAE;EACpC;EACA,OAAO,IAAAC,wBAAkB,EAACZ,OAAO,CAACa,IAAI,CAAC,IAAI,IAAAF,+BAAyB,GAAE;AACxE;AAEO,SAASP,YAAY,CAACJ,OAAgB,EAAE;EAC7C,IAAIA,OAAO,CAACM,cAAc,KAAK,KAAK,EAAE;IACpC,OAAO,IAAAQ,6BAAwB,GAAE;EACnC;EAEA,OAAO,IAAAC,sBAAiB,EAACf,OAAO,CAACa,IAAI,CAAC,IAAI,IAAAC,6BAAwB,GAAE;AACtE;AAEO,SAASE,YAAY,CAAChB,OAAgB,EAAE;EAC7C,IAAIA,OAAO,CAACM,cAAc,KAAK,KAAK,EAAE;IACpC,OAAO,IAAAW,6BAAwB,GAAE;EACnC;EAEA,OAAO,IAAAC,sBAAiB,EAAClB,OAAO,CAACa,IAAI,CAAC,IAAI,IAAAI,6BAAwB,GAAE;AACtE;AAEO,SAAS3B,IAAI,CAACU,OAAgB,EAAE;EACrC,OAAOH,uBAAuB,CAAC,EAAE,EAAE,MAAM,EAAEG,OAAO,CAAC;AACrD;AAEO,SAAST,OAAO,CAACO,YAA2B,EAAEE,OAAgB,EAAE;EACrE,OAAOH,uBAAuB,CAACC,YAAY,EAAE,SAAS,EAAEE,OAAO,CAAC;AAClE;AAEO,SAASR,UAAU,CAACM,YAA2B,EAAEE,OAAgB,EAAE;EACxE,OAAOH,uBAAuB,CAACC,YAAY,EAAE,YAAY,EAAEE,OAAO,CAAC;AACrE;AAEO,SAASP,SAAS,CAACK,YAA2B,EAAEE,OAAgB,EAAE;EACvE,OAAOH,uBAAuB,CAACC,YAAY,EAAE,WAAW,EAAEE,OAAO,CAAC;AACpE;AAEO,SAASN,UAAU,CAACM,OAAgB,EAAE;EAC3C,OAAOH,uBAAuB,CAAC,EAAE,EAAE,YAAY,EAAEG,OAAO,CAAC;AAC3D"}