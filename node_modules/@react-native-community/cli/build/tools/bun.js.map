{"version": 3, "names": ["getBunVersionIfAvailable", "bunVersion", "execSync", "stdio", "toString", "trim", "error", "semver", "gte", "logger", "isProjectUsingBun", "cwd", "findUp", "sync"], "sources": ["../../src/tools/bun.ts"], "sourcesContent": ["import {logger} from '@react-native-community/cli-tools';\nimport {execSync} from 'child_process';\nimport findUp from 'find-up';\nimport semver from 'semver';\n\nexport function getBunVersionIfAvailable() {\n  let bunVersion;\n\n  try {\n    bunVersion = (\n      execSync('bun --version', {\n        stdio: [0, 'pipe', 'ignore'],\n      }).toString() || ''\n    ).trim();\n  } catch (error) {\n    return null;\n  }\n\n  try {\n    if (semver.gte(bunVersion, '1.0.0')) {\n      return bunVersion;\n    }\n    return null;\n  } catch (error) {\n    logger.error(`Cannot parse bun version: ${bunVersion}`);\n    return null;\n  }\n}\n\nexport function isProjectUsingBun(cwd: string) {\n  return findUp.sync('bun.lockb', {cwd});\n}\n"], "mappings": ";;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA4B;AAErB,SAASA,wBAAwB,GAAG;EACzC,IAAIC,UAAU;EAEd,IAAI;IACFA,UAAU,GAAG,CACX,IAAAC,yBAAQ,EAAC,eAAe,EAAE;MACxBC,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,QAAQ;IAC7B,CAAC,CAAC,CAACC,QAAQ,EAAE,IAAI,EAAE,EACnBC,IAAI,EAAE;EACV,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAO,IAAI;EACb;EAEA,IAAI;IACF,IAAIC,iBAAM,CAACC,GAAG,CAACP,UAAU,EAAE,OAAO,CAAC,EAAE;MACnC,OAAOA,UAAU;IACnB;IACA,OAAO,IAAI;EACb,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdG,kBAAM,CAACH,KAAK,CAAE,6BAA4BL,UAAW,EAAC,CAAC;IACvD,OAAO,IAAI;EACb;AACF;AAEO,SAASS,iBAAiB,CAACC,GAAW,EAAE;EAC7C,OAAOC,iBAAM,CAACC,IAAI,CAAC,WAAW,EAAE;IAACF;EAAG,CAAC,CAAC;AACxC"}