{"version": 3, "names": ["executeCommand", "command", "args", "options", "execa", "stdio", "silent", "logger", "isVerbose", "cwd", "root"], "sources": ["../../src/tools/executeCommand.ts"], "sourcesContent": ["import {logger} from '@react-native-community/cli-tools';\nimport execa from 'execa';\n\nexport function executeCommand(\n  command: string,\n  args: Array<string>,\n  options: {\n    root: string;\n    silent?: boolean;\n  },\n) {\n  return execa(command, args, {\n    stdio: options.silent && !logger.isVerbose() ? 'pipe' : 'inherit',\n    cwd: options.root,\n  });\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0B;AAEnB,SAASA,cAAc,CAC5BC,OAAe,EACfC,IAAmB,EACnBC,OAGC,EACD;EACA,OAAO,IAAAC,gBAAK,EAACH,OAAO,EAAEC,IAAI,EAAE;IAC1BG,KAAK,EAAEF,OAAO,CAACG,MAAM,IAAI,CAACC,kBAAM,CAACC,SAAS,EAAE,GAAG,MAAM,GAAG,SAAS;IACjEC,GAAG,EAAEN,OAAO,CAACO;EACf,CAAC,CAAC;AACJ"}