"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
function _path() {
  const data = _interopRequireDefault(require("path"));
  _path = function () {
    return data;
  };
  return data;
}
function _fs() {
  const data = _interopRequireDefault(require("fs"));
  _fs = function () {
    return data;
  };
  return data;
}
function _process() {
  const data = _interopRequireDefault(require("process"));
  _process = function () {
    return data;
  };
  return data;
}
function _chalk() {
  const data = _interopRequireDefault(require("chalk"));
  _chalk = function () {
    return data;
  };
  return data;
}
function _cliTools() {
  const data = require("@react-native-community/cli-tools");
  _cliTools = function () {
    return data;
  };
  return data;
}
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 */

function printRunInstructions(projectDir, projectName, options = {
  showPodsInstructions: false
}) {
  let iosInstructions = '';
  let desktopInstructions = '';
  if (_process().default.platform === 'darwin') {
    const iosProjectDir = _path().default.resolve(projectDir, 'ios');
    const iosPodsFile = _path().default.resolve(iosProjectDir, `${projectName}.xcworkspace`);
    const isUsingPods = _fs().default.existsSync(iosPodsFile);
    const relativeXcodeProjectPath = _path().default.relative('..', isUsingPods ? iosPodsFile : _path().default.resolve(iosProjectDir, `${projectName}.xcodeproj`));
    iosInstructions = `
  ${_chalk().default.cyan(`Run instructions for ${_chalk().default.bold('iOS')}`)}:
    • cd "${projectDir}${options.showPodsInstructions ? '/ios' : ''}"
    ${options.showPodsInstructions ? `
    • Install Cocoapods
      • bundle install # you need to run this only once in your project.
      • bundle exec pod install
      • cd ..
    ` : ''}
    • npx react-native run-ios
    ${_chalk().default.dim('- or -')}
    • Open ${relativeXcodeProjectPath} in Xcode or run "xed -b ios"
    • Hit the Run button
    `;
    desktopInstructions = `
  ${_chalk().default.magenta(`Run instructions for ${_chalk().default.bold('macOS')}`)}:
    • See ${_chalk().default.underline('https://aka.ms/ReactNativeGuideMacOS')} for the latest up-to-date instructions.
    `;
  }
  if (_process().default.platform === 'win32') {
    desktopInstructions = `
  ${_chalk().default.cyan(`Run instructions for ${_chalk().default.bold('Windows')}`)}:
    • See ${_chalk().default.underline('https://aka.ms/ReactNativeGuideWindows')} for the latest up-to-date instructions.
    `;
  }
  const androidInstructions = `
  ${_chalk().default.green(`Run instructions for ${_chalk().default.bold('Android')}`)}:
    • Have an Android emulator running (quickest way to get started), or a device connected.
    • cd "${projectDir}" && npx react-native run-android
  `;
  _cliTools().logger.log(`
  ${androidInstructions}${iosInstructions}${desktopInstructions}
  `);
}
var _default = printRunInstructions;
exports.default = _default;

//# sourceMappingURL=/Users/<USER>/Developer/oss/rncli/packages/cli/build/commands/init/printRunInstructions.js.map