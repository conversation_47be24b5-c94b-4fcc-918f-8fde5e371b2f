"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TEMPLATE_PACKAGE_LEGACY_TYPESCRIPT = exports.TEMPLATE_PACKAGE_LEGACY = exports.TEMPLATE_PACKAGE_COMMUNITY = exports.TEMPLATE_COMMUNITY_REACT_NATIVE_VERSION = void 0;
const TEMPLATE_PACKAGE_COMMUNITY = '@react-native-community/template';
exports.TEMPLATE_PACKAGE_COMMUNITY = TEMPLATE_PACKAGE_COMMUNITY;
const TEMPLATE_PACKAGE_LEGACY = 'react-native';
exports.TEMPLATE_PACKAGE_LEGACY = TEMPLATE_PACKAGE_LEGACY;
const TEMPLATE_PACKAGE_LEGACY_TYPESCRIPT = 'react-native-template-typescript';

// This version moved from inlining the template to using @react-native-community/template
exports.TEMPLATE_PACKAGE_LEGACY_TYPESCRIPT = TEMPLATE_PACKAGE_LEGACY_TYPESCRIPT;
const TEMPLATE_COMMUNITY_REACT_NATIVE_VERSION = '0.75.0';
exports.TEMPLATE_COMMUNITY_REACT_NATIVE_VERSION = TEMPLATE_COMMUNITY_REACT_NATIVE_VERSION;

//# sourceMappingURL=/Users/<USER>/Developer/oss/rncli/packages/cli/build/commands/init/constants.js.map