{"version": 3, "names": ["createTemplateUri", "options", "version", "platformName", "logger", "debug", "template", "TEMPLATE_PACKAGE_LEGACY_TYPESCRIPT", "warn", "TEMPLATE_PACKAGE_LEGACY", "simpleVersion", "semver", "coerce", "useLegacyTemplate", "lt", "TEMPLATE_COMMUNITY_REACT_NATIVE_VERSION", "test", "TEMPLATE_PACKAGE_COMMUNITY", "templateVersion", "getTemplateVersion"], "sources": ["../../../src/commands/init/version.ts"], "sourcesContent": ["import {logger} from '@react-native-community/cli-tools';\nimport {getTemplateVersion} from '../../tools/npm';\nimport semver from 'semver';\n\nimport type {Options} from './types';\nimport {\n  TEMPLATE_COMMUNITY_REACT_NATIVE_VERSION,\n  TEMPLATE_PACKAGE_COMMUNITY,\n  TEMPLATE_PACKAGE_LEGACY,\n  TEMPLATE_PACKAGE_LEGACY_TYPESCRIPT,\n} from './constants';\n\nexport async function createTemplateUri(\n  options: Options,\n  version: string,\n): Promise<string> {\n  if (options.platformName && options.platformName !== 'react-native') {\n    logger.debug('User has specified an out-of-tree platform, using it');\n    return `${options.platformName}@${version}`;\n  }\n\n  if (options.template === TEMPLATE_PACKAGE_LEGACY_TYPESCRIPT) {\n    logger.warn(\n      \"Ignoring custom template: 'react-native-template-typescript'. Starting from React Native v0.71 TypeScript is used by default.\",\n    );\n    return TEMPLATE_PACKAGE_LEGACY;\n  }\n\n  if (options.template) {\n    logger.debug(`Use the user provided --template=${options.template}`);\n    return options.template;\n  }\n\n  // 0.75.0-nightly-20240618-5df5ed1a8' -> 0.75.0\n  // 0.75.0-rc.1 -> 0.75.0\n  const simpleVersion = semver.coerce(version) ?? version;\n\n  // Does the react-native@version package *not* have a template embedded. We know that this applies to\n  // all version before 0.75. The 1st release candidate is the minimal version that has no template.\n  const useLegacyTemplate = semver.lt(\n    simpleVersion,\n    TEMPLATE_COMMUNITY_REACT_NATIVE_VERSION,\n  );\n\n  logger.debug(\n    `[template]: is '${version} (${simpleVersion})' < '${TEMPLATE_COMMUNITY_REACT_NATIVE_VERSION}' = ` +\n      (useLegacyTemplate\n        ? 'yes, look for template in react-native'\n        : 'no, look for template in @react-native-community/template'),\n  );\n\n  if (!useLegacyTemplate) {\n    if (/nightly/.test(version)) {\n      logger.debug(\n        \"[template]: you're using a nightly version of react-native\",\n      );\n      // Template nightly versions and react-native@nightly versions don't match (template releases at a much\n      // lower cadence). We have to assume the user is running against the latest nightly by pointing to the tag.\n      return `${TEMPLATE_PACKAGE_COMMUNITY}@nightly`;\n    }\n    const templateVersion = await getTemplateVersion(version);\n    return `${TEMPLATE_PACKAGE_COMMUNITY}@${templateVersion}`;\n  }\n\n  logger.debug(\n    `Using the legacy template because '${TEMPLATE_PACKAGE_LEGACY}' still contains a template folder`,\n  );\n  return `${TEMPLATE_PACKAGE_LEGACY}@${version}`;\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAGA;AAKqB;AAEd,eAAeA,iBAAiB,CACrCC,OAAgB,EAChBC,OAAe,EACE;EACjB,IAAID,OAAO,CAACE,YAAY,IAAIF,OAAO,CAACE,YAAY,KAAK,cAAc,EAAE;IACnEC,kBAAM,CAACC,KAAK,CAAC,sDAAsD,CAAC;IACpE,OAAQ,GAAEJ,OAAO,CAACE,YAAa,IAAGD,OAAQ,EAAC;EAC7C;EAEA,IAAID,OAAO,CAACK,QAAQ,KAAKC,6CAAkC,EAAE;IAC3DH,kBAAM,CAACI,IAAI,CACT,+HAA+H,CAChI;IACD,OAAOC,kCAAuB;EAChC;EAEA,IAAIR,OAAO,CAACK,QAAQ,EAAE;IACpBF,kBAAM,CAACC,KAAK,CAAE,oCAAmCJ,OAAO,CAACK,QAAS,EAAC,CAAC;IACpE,OAAOL,OAAO,CAACK,QAAQ;EACzB;;EAEA;EACA;EACA,MAAMI,aAAa,GAAGC,iBAAM,CAACC,MAAM,CAACV,OAAO,CAAC,IAAIA,OAAO;;EAEvD;EACA;EACA,MAAMW,iBAAiB,GAAGF,iBAAM,CAACG,EAAE,CACjCJ,aAAa,EACbK,kDAAuC,CACxC;EAEDX,kBAAM,CAACC,KAAK,CACT,mBAAkBH,OAAQ,KAAIQ,aAAc,SAAQK,kDAAwC,MAAK,IAC/FF,iBAAiB,GACd,wCAAwC,GACxC,2DAA2D,CAAC,CACnE;EAED,IAAI,CAACA,iBAAiB,EAAE;IACtB,IAAI,SAAS,CAACG,IAAI,CAACd,OAAO,CAAC,EAAE;MAC3BE,kBAAM,CAACC,KAAK,CACV,4DAA4D,CAC7D;MACD;MACA;MACA,OAAQ,GAAEY,qCAA2B,UAAS;IAChD;IACA,MAAMC,eAAe,GAAG,MAAM,IAAAC,uBAAkB,EAACjB,OAAO,CAAC;IACzD,OAAQ,GAAEe,qCAA2B,IAAGC,eAAgB,EAAC;EAC3D;EAEAd,kBAAM,CAACC,KAAK,CACT,sCAAqCI,kCAAwB,oCAAmC,CAClG;EACD,OAAQ,GAAEA,kCAAwB,IAAGP,OAAQ,EAAC;AAChD"}