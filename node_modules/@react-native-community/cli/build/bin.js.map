{"version": 3, "names": ["semver", "satisfies", "process", "version", "versionRanges", "NODE_JS", "run", "require", "console", "error", "chalk", "red"], "sources": ["../src/bin.ts"], "sourcesContent": ["#!/usr/bin/env node\n\nimport './tools/gracefulifyFs';\nimport semver from 'semver';\nimport chalk from 'chalk';\nimport {versionRanges} from '@react-native-community/cli-doctor';\n\nif (semver.satisfies(process.version, versionRanges.NODE_JS)) {\n  const {run} = require('./');\n  run();\n} else {\n  console.error(\n    `${chalk.red(\n      `React Native needs Node.js ${versionRanges.NODE_JS}. You're currently on version ${process.version}. Please upgrade Node.js to a supported version and try again.`,\n    )}`,\n  );\n}\n"], "mappings": "AAAA;AAAmB;;AAEnB;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAiE;AAEjE,IAAIA,iBAAM,CAACC,SAAS,CAACC,OAAO,CAACC,OAAO,EAAEC,0BAAa,CAACC,OAAO,CAAC,EAAE;EAC5D,MAAM;IAACC;EAAG,CAAC,GAAGC,OAAO,CAAC,IAAI,CAAC;EAC3BD,GAAG,EAAE;AACP,CAAC,MAAM;EACLE,OAAO,CAACC,KAAK,CACV,GAAEC,gBAAK,CAACC,GAAG,CACT,8BAA6BP,0BAAa,CAACC,OAAQ,iCAAgCH,OAAO,CAACC,OAAQ,gEAA+D,CACnK,EAAC,CACJ;AACH"}