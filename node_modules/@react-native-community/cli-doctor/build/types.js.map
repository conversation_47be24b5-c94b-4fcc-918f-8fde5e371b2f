{"version": 3, "names": [], "sources": ["../src/types.ts"], "sourcesContent": ["import type {Config} from '@react-native-community/cli-types';\nimport type {Ora} from 'ora';\n\nexport type Loader = Ora;\n\nexport type NotFound = 'Not Found';\n\ntype AvailableInformation = {\n  version: string;\n  path: string;\n};\n\ntype Information = AvailableInformation | NotFound;\n\nexport type EnvironmentInfo = {\n  System: {\n    OS: string;\n    CPU: string;\n    Memory: string;\n    Shell: AvailableInformation;\n  };\n  Binaries: {\n    Node: AvailableInformation;\n    Yarn: AvailableInformation;\n    npm: AvailableInformation;\n    bun: AvailableInformation;\n    Watchman: AvailableInformation;\n  };\n  Managers: {\n    CocoaPods: AvailableInformation;\n  };\n  SDKs: {\n    'iOS SDK': {\n      Platforms: string[];\n    };\n    'Android SDK':\n      | {\n          'API Levels': string[] | NotFound;\n          'Build Tools': string[] | NotFound;\n          'System Images': string[] | NotFound;\n          'Android NDK': string | NotFound;\n        }\n      | NotFound;\n  };\n  IDEs: {\n    'Android Studio': AvailableInformation | NotFound;\n    Emacs: AvailableInformation;\n    Nano: AvailableInformation;\n    VSCode: AvailableInformation;\n    Vim: AvailableInformation;\n    Xcode: AvailableInformation;\n  };\n  Languages: {\n    Java: Information;\n    Ruby: AvailableInformation;\n  };\n};\n\nexport type HealthCheckCategory = {\n  label: string;\n  healthchecks: HealthCheckInterface[];\n};\n\nexport type Healthchecks = {\n  common: HealthCheckCategory;\n  android: HealthCheckCategory;\n  ios?: HealthCheckCategory;\n};\n\nexport type RunAutomaticFix = (args: {\n  loader: Loader;\n  logManualInstallation: ({\n    healthcheck,\n    url,\n    command,\n    message,\n  }: {\n    healthcheck?: string;\n    url?: string;\n    command?: string;\n    message?: string;\n  }) => void;\n  environmentInfo: EnvironmentInfo;\n  config?: Config;\n}) => Promise<void> | void;\n\nexport type HealthCheckInterface = {\n  label: string;\n  visible?: boolean | void;\n  isRequired?: boolean;\n  description: string;\n  getDiagnostics: (\n    environmentInfo: EnvironmentInfo,\n    config?: Config,\n  ) => Promise<{\n    description?: string;\n    version?: string;\n    versions?: [string];\n    versionRange?: string;\n    needsToBeFixed: boolean | string;\n  }>;\n  win32AutomaticFix?: RunAutomaticFix;\n  darwinAutomaticFix?: RunAutomaticFix;\n  linuxAutomaticFix?: RunAutomaticFix;\n  runAutomaticFix: RunAutomaticFix;\n};\n\nexport type HealthCheckResult = {\n  label: string;\n  needsToBeFixed: boolean;\n  version?: NotFound | string;\n  versions?: [string] | string;\n  versionRange?: string;\n  description: string | undefined;\n  runAutomaticFix: RunAutomaticFix;\n  isRequired: boolean;\n  type?: string;\n};\n\nexport type HealthCheckCategoryResult = {\n  label: string;\n  healthchecks: HealthCheckResult[];\n};\n"], "mappings": ""}