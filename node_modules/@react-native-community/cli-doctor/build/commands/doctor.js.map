{"version": 3, "names": ["printCategory", "label", "key", "logger", "log", "chalk", "dim", "printVersions", "version", "versions", "versionRange", "versionsToShow", "Array", "isArray", "join", "logMessage", "red", "green", "printIssue", "needsToBeFixed", "isRequired", "description", "symbol", "yellow", "descriptionToShow", "printOverallStats", "errors", "warnings", "bold", "getAutomaticFixForPlatform", "healthcheck", "platform", "win32AutomaticFix", "runAutomaticFix", "darwinAutomaticFix", "linuxAutomaticFix", "<PERSON><PERSON><PERSON><PERSON>", "_", "options", "config", "loader", "<PERSON><PERSON><PERSON><PERSON>", "start", "environmentInfo", "getEnvironmentInfo", "iterateOverHealthChecks", "healthchecks", "Promise", "all", "map", "visible", "getDiagnostics", "isWarning", "Boolean", "process", "type", "HEALTHCHECK_TYPES", "WARNING", "ERROR", "undefined", "filter", "removeFixedCategories", "categories", "category", "some", "iterateOverCategories", "healthchecksPerCategory", "Object", "values", "getHealthchecks", "stop", "stats", "for<PERSON>ach", "issueCategory", "fix", "automaticFixLevel", "AUTOMATIC_FIX_LEVELS", "ALL_ISSUES", "removeKeyPressListener", "stdin", "setRawMode", "removeAllListeners", "onKeyPress", "KEYS", "EXIT", "exit", "FIX_ALL_ISSUES", "FIX_ERRORS", "FIX_WARNINGS", "includes", "ERRORS", "WARNINGS", "err", "stderr", "stdout", "CLIError", "printFixOptions", "func", "detached", "name"], "sources": ["../../src/commands/doctor.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport {logger, get<PERSON><PERSON><PERSON>, C<PERSON>IError} from '@react-native-community/cli-tools';\nimport {getHealthchecks, HEALTHCHECK_TYPES} from '../tools/healthchecks';\nimport printFixOptions, {KEYS} from '../tools/printFixOptions';\nimport runAutomaticFix, {AUTOMATIC_FIX_LEVELS} from '../tools/runAutomaticFix';\nimport {DetachedCommandFunction} from '@react-native-community/cli-types';\nimport {\n  HealthCheckCategoryResult,\n  HealthCheckInterface,\n  HealthCheckCategory,\n  HealthCheckResult,\n} from '../types';\nimport getEnvironmentInfo from '../tools/envinfo';\nimport {logMessage} from '../tools/healthchecks/common';\n\nconst printCategory = ({label, key}: {label: string; key: number}) => {\n  if (key > 0) {\n    logger.log();\n  }\n\n  logger.log(chalk.dim(label));\n};\n\nconst printVersions = ({\n  version,\n  versions,\n  versionRang<PERSON>,\n}: {\n  version?: 'Not Found' | string;\n  versions?: [string] | string;\n  versionRange: string;\n}) => {\n  if (versions) {\n    const versionsToShow = Array.isArray(versions)\n      ? versions.join(', ')\n      : 'N/A';\n\n    logMessage(`- Versions found: ${chalk.red(versionsToShow)}`);\n    logMessage(`- Version supported: ${chalk.green(versionRange)}`);\n\n    return;\n  }\n\n  const versionsToShow = version && version !== 'Not Found' ? version : 'N/A';\n\n  logMessage(`- Version found: ${chalk.red(versionsToShow)}`);\n  logMessage(`- Version supported: ${chalk.green(versionRange)}`);\n\n  return;\n};\n\nconst printIssue = ({\n  label,\n  needsToBeFixed,\n  version,\n  versions,\n  versionRange,\n  isRequired,\n  description,\n}: HealthCheckResult) => {\n  const symbol = needsToBeFixed\n    ? isRequired\n      ? chalk.red('✖')\n      : chalk.yellow('●')\n    : chalk.green('✓');\n\n  const descriptionToShow = description ? ` - ${description}` : '';\n\n  logger.log(` ${symbol} ${label}${descriptionToShow}`);\n\n  if (needsToBeFixed && versionRange) {\n    return printVersions({version, versions, versionRange});\n  }\n};\n\nconst printOverallStats = ({\n  errors,\n  warnings,\n}: {\n  errors: number;\n  warnings: number;\n}) => {\n  logger.log(`\\n${chalk.bold('Errors:')}   ${errors}`);\n  logger.log(`${chalk.bold('Warnings:')} ${warnings}`);\n};\n\ntype FlagsT = {\n  fix: boolean | void;\n  contributor: boolean | void;\n};\n\n/**\n * Given a `healthcheck` and a `platform`, returns the specific fix for\n * it or the fallback one if there is not one (`runAutomaticFix`).\n */\nconst getAutomaticFixForPlatform = (\n  healthcheck: HealthCheckInterface,\n  platform: NodeJS.Platform,\n) => {\n  switch (platform) {\n    case 'win32':\n      return healthcheck.win32AutomaticFix || healthcheck.runAutomaticFix;\n    case 'darwin':\n      return healthcheck.darwinAutomaticFix || healthcheck.runAutomaticFix;\n    case 'linux':\n      return healthcheck.linuxAutomaticFix || healthcheck.runAutomaticFix;\n    default:\n      return healthcheck.runAutomaticFix;\n  }\n};\n\nconst doctorCommand = (async (_, options, config) => {\n  const loader = getLoader();\n\n  loader.start('Running diagnostics...');\n\n  const environmentInfo = await getEnvironmentInfo();\n\n  const iterateOverHealthChecks = async ({\n    label,\n    healthchecks,\n  }: HealthCheckCategory): Promise<HealthCheckCategoryResult> => ({\n    label,\n    healthchecks: (\n      await Promise.all(\n        healthchecks.map(async (healthcheck) => {\n          if (healthcheck.visible === false) {\n            return;\n          }\n\n          const {description, needsToBeFixed, version, versions, versionRange} =\n            await healthcheck.getDiagnostics(environmentInfo, config);\n\n          // Assume that it's required unless specified otherwise\n          const isRequired = healthcheck.isRequired !== false;\n          const isWarning = needsToBeFixed && !isRequired;\n\n          return {\n            label: healthcheck.label,\n            needsToBeFixed: Boolean(needsToBeFixed),\n            version,\n            versions,\n            versionRange,\n            description: description ?? healthcheck.description,\n            runAutomaticFix: getAutomaticFixForPlatform(\n              healthcheck,\n              process.platform,\n            ),\n            isRequired,\n            type: needsToBeFixed\n              ? isWarning\n                ? HEALTHCHECK_TYPES.WARNING\n                : HEALTHCHECK_TYPES.ERROR\n              : undefined,\n          };\n        }),\n      )\n    ).filter((healthcheck) => healthcheck !== undefined) as HealthCheckResult[],\n  });\n\n  // Remove all the categories that don't have any healthcheck with\n  // `needsToBeFixed` so they don't show when the user taps to fix encountered\n  // issues\n  const removeFixedCategories = (categories: HealthCheckCategoryResult[]) =>\n    categories.filter((category) =>\n      category.healthchecks.some((healthcheck) => healthcheck.needsToBeFixed),\n    );\n\n  const iterateOverCategories = (categories: HealthCheckCategory[]) =>\n    Promise.all(categories.map(iterateOverHealthChecks));\n\n  const healthchecksPerCategory = await iterateOverCategories(\n    Object.values(await getHealthchecks(options)).filter(\n      (category) => category !== undefined,\n    ) as HealthCheckCategory[],\n  );\n\n  loader.stop();\n\n  const stats = {\n    errors: 0,\n    warnings: 0,\n  };\n\n  healthchecksPerCategory.forEach((issueCategory, key) => {\n    printCategory({...issueCategory, key});\n\n    issueCategory.healthchecks.forEach((healthcheck) => {\n      printIssue(healthcheck);\n\n      if (healthcheck.type === HEALTHCHECK_TYPES.WARNING) {\n        stats.warnings++;\n        return;\n      }\n\n      if (healthcheck.type === HEALTHCHECK_TYPES.ERROR) {\n        stats.errors++;\n        return;\n      }\n    });\n  });\n\n  printOverallStats(stats);\n\n  if (options.fix) {\n    return await runAutomaticFix({\n      healthchecks: removeFixedCategories(healthchecksPerCategory),\n      automaticFixLevel: AUTOMATIC_FIX_LEVELS.ALL_ISSUES,\n      stats,\n      loader,\n      environmentInfo,\n      config,\n    });\n  }\n\n  const removeKeyPressListener = () => {\n    if (typeof process.stdin.setRawMode === 'function') {\n      process.stdin.setRawMode(false);\n    }\n    process.stdin.removeAllListeners('data');\n  };\n\n  const onKeyPress = async (key: string) => {\n    if (key === KEYS.EXIT || key === '\\u0003') {\n      removeKeyPressListener();\n\n      process.exit(0);\n    }\n\n    if (\n      [KEYS.FIX_ALL_ISSUES, KEYS.FIX_ERRORS, KEYS.FIX_WARNINGS].includes(key)\n    ) {\n      removeKeyPressListener();\n\n      try {\n        const automaticFixLevel = {\n          [KEYS.FIX_ALL_ISSUES]: AUTOMATIC_FIX_LEVELS.ALL_ISSUES,\n          [KEYS.FIX_ERRORS]: AUTOMATIC_FIX_LEVELS.ERRORS,\n          [KEYS.FIX_WARNINGS]: AUTOMATIC_FIX_LEVELS.WARNINGS,\n        };\n\n        await runAutomaticFix({\n          healthchecks: removeFixedCategories(healthchecksPerCategory),\n          automaticFixLevel: automaticFixLevel[key],\n          stats,\n          loader,\n          environmentInfo,\n          config,\n        });\n\n        process.exit(0);\n      } catch (err) {\n        logger.log((err as any).stderr || (err as any).stdout);\n        throw new CLIError('Failed to run automatic fixes.', err as Error);\n      }\n    }\n  };\n\n  if (stats.errors || stats.warnings) {\n    printFixOptions({onKeyPress});\n  }\n}) as DetachedCommandFunction<FlagsT>;\n\nexport default {\n  func: doctorCommand,\n  detached: true,\n  name: 'doctor',\n  description:\n    'Diagnose and fix common Node.js, iOS, Android & React Native issues.',\n  options: [\n    {\n      name: '--fix',\n      description: 'Attempt to fix all diagnosed issues.',\n    },\n    {\n      name: '--contributor',\n      description:\n        'Add healthchecks required to installations required for contributing to React Native.',\n    },\n  ],\n};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;AAQA;AACA;AAAwD;AAAA;AAAA;AAExD,MAAMA,aAAa,GAAG,CAAC;EAACC,KAAK;EAAEC;AAAiC,CAAC,KAAK;EACpE,IAAIA,GAAG,GAAG,CAAC,EAAE;IACXC,kBAAM,CAACC,GAAG,EAAE;EACd;EAEAD,kBAAM,CAACC,GAAG,CAACC,gBAAK,CAACC,GAAG,CAACL,KAAK,CAAC,CAAC;AAC9B,CAAC;AAED,MAAMM,aAAa,GAAG,CAAC;EACrBC,OAAO;EACPC,QAAQ;EACRC;AAKF,CAAC,KAAK;EACJ,IAAID,QAAQ,EAAE;IACZ,MAAME,cAAc,GAAGC,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,GAC1CA,QAAQ,CAACK,IAAI,CAAC,IAAI,CAAC,GACnB,KAAK;IAET,IAAAC,kBAAU,EAAE,qBAAoBV,gBAAK,CAACW,GAAG,CAACL,cAAc,CAAE,EAAC,CAAC;IAC5D,IAAAI,kBAAU,EAAE,wBAAuBV,gBAAK,CAACY,KAAK,CAACP,YAAY,CAAE,EAAC,CAAC;IAE/D;EACF;EAEA,MAAMC,cAAc,GAAGH,OAAO,IAAIA,OAAO,KAAK,WAAW,GAAGA,OAAO,GAAG,KAAK;EAE3E,IAAAO,kBAAU,EAAE,oBAAmBV,gBAAK,CAACW,GAAG,CAACL,cAAc,CAAE,EAAC,CAAC;EAC3D,IAAAI,kBAAU,EAAE,wBAAuBV,gBAAK,CAACY,KAAK,CAACP,YAAY,CAAE,EAAC,CAAC;EAE/D;AACF,CAAC;AAED,MAAMQ,UAAU,GAAG,CAAC;EAClBjB,KAAK;EACLkB,cAAc;EACdX,OAAO;EACPC,QAAQ;EACRC,YAAY;EACZU,UAAU;EACVC;AACiB,CAAC,KAAK;EACvB,MAAMC,MAAM,GAAGH,cAAc,GACzBC,UAAU,GACRf,gBAAK,CAACW,GAAG,CAAC,GAAG,CAAC,GACdX,gBAAK,CAACkB,MAAM,CAAC,GAAG,CAAC,GACnBlB,gBAAK,CAACY,KAAK,CAAC,GAAG,CAAC;EAEpB,MAAMO,iBAAiB,GAAGH,WAAW,GAAI,MAAKA,WAAY,EAAC,GAAG,EAAE;EAEhElB,kBAAM,CAACC,GAAG,CAAE,IAAGkB,MAAO,IAAGrB,KAAM,GAAEuB,iBAAkB,EAAC,CAAC;EAErD,IAAIL,cAAc,IAAIT,YAAY,EAAE;IAClC,OAAOH,aAAa,CAAC;MAACC,OAAO;MAAEC,QAAQ;MAAEC;IAAY,CAAC,CAAC;EACzD;AACF,CAAC;AAED,MAAMe,iBAAiB,GAAG,CAAC;EACzBC,MAAM;EACNC;AAIF,CAAC,KAAK;EACJxB,kBAAM,CAACC,GAAG,CAAE,KAAIC,gBAAK,CAACuB,IAAI,CAAC,SAAS,CAAE,MAAKF,MAAO,EAAC,CAAC;EACpDvB,kBAAM,CAACC,GAAG,CAAE,GAAEC,gBAAK,CAACuB,IAAI,CAAC,WAAW,CAAE,IAAGD,QAAS,EAAC,CAAC;AACtD,CAAC;AAOD;AACA;AACA;AACA;AACA,MAAME,0BAA0B,GAAG,CACjCC,WAAiC,EACjCC,QAAyB,KACtB;EACH,QAAQA,QAAQ;IACd,KAAK,OAAO;MACV,OAAOD,WAAW,CAACE,iBAAiB,IAAIF,WAAW,CAACG,eAAe;IACrE,KAAK,QAAQ;MACX,OAAOH,WAAW,CAACI,kBAAkB,IAAIJ,WAAW,CAACG,eAAe;IACtE,KAAK,OAAO;MACV,OAAOH,WAAW,CAACK,iBAAiB,IAAIL,WAAW,CAACG,eAAe;IACrE;MACE,OAAOH,WAAW,CAACG,eAAe;EAAC;AAEzC,CAAC;AAED,MAAMG,aAAa,GAAI,OAAOC,CAAC,EAAEC,OAAO,EAAEC,MAAM,KAAK;EACnD,MAAMC,MAAM,GAAG,IAAAC,qBAAS,GAAE;EAE1BD,MAAM,CAACE,KAAK,CAAC,wBAAwB,CAAC;EAEtC,MAAMC,eAAe,GAAG,MAAM,IAAAC,gBAAkB,GAAE;EAElD,MAAMC,uBAAuB,GAAG,OAAO;IACrC5C,KAAK;IACL6C;EACmB,CAAC,MAA0C;IAC9D7C,KAAK;IACL6C,YAAY,EAAE,CACZ,MAAMC,OAAO,CAACC,GAAG,CACfF,YAAY,CAACG,GAAG,CAAC,MAAOnB,WAAW,IAAK;MACtC,IAAIA,WAAW,CAACoB,OAAO,KAAK,KAAK,EAAE;QACjC;MACF;MAEA,MAAM;QAAC7B,WAAW;QAAEF,cAAc;QAAEX,OAAO;QAAEC,QAAQ;QAAEC;MAAY,CAAC,GAClE,MAAMoB,WAAW,CAACqB,cAAc,CAACR,eAAe,EAAEJ,MAAM,CAAC;;MAE3D;MACA,MAAMnB,UAAU,GAAGU,WAAW,CAACV,UAAU,KAAK,KAAK;MACnD,MAAMgC,SAAS,GAAGjC,cAAc,IAAI,CAACC,UAAU;MAE/C,OAAO;QACLnB,KAAK,EAAE6B,WAAW,CAAC7B,KAAK;QACxBkB,cAAc,EAAEkC,OAAO,CAAClC,cAAc,CAAC;QACvCX,OAAO;QACPC,QAAQ;QACRC,YAAY;QACZW,WAAW,EAAEA,WAAW,IAAIS,WAAW,CAACT,WAAW;QACnDY,eAAe,EAAEJ,0BAA0B,CACzCC,WAAW,EACXwB,OAAO,CAACvB,QAAQ,CACjB;QACDX,UAAU;QACVmC,IAAI,EAAEpC,cAAc,GAChBiC,SAAS,GACPI,+BAAiB,CAACC,OAAO,GACzBD,+BAAiB,CAACE,KAAK,GACzBC;MACN,CAAC;IACH,CAAC,CAAC,CACH,EACDC,MAAM,CAAE9B,WAAW,IAAKA,WAAW,KAAK6B,SAAS;EACrD,CAAC,CAAC;;EAEF;EACA;EACA;EACA,MAAME,qBAAqB,GAAIC,UAAuC,IACpEA,UAAU,CAACF,MAAM,CAAEG,QAAQ,IACzBA,QAAQ,CAACjB,YAAY,CAACkB,IAAI,CAAElC,WAAW,IAAKA,WAAW,CAACX,cAAc,CAAC,CACxE;EAEH,MAAM8C,qBAAqB,GAAIH,UAAiC,IAC9Df,OAAO,CAACC,GAAG,CAACc,UAAU,CAACb,GAAG,CAACJ,uBAAuB,CAAC,CAAC;EAEtD,MAAMqB,uBAAuB,GAAG,MAAMD,qBAAqB,CACzDE,MAAM,CAACC,MAAM,CAAC,MAAM,IAAAC,6BAAe,EAAC/B,OAAO,CAAC,CAAC,CAACsB,MAAM,CACjDG,QAAQ,IAAKA,QAAQ,KAAKJ,SAAS,CACrC,CACF;EAEDnB,MAAM,CAAC8B,IAAI,EAAE;EAEb,MAAMC,KAAK,GAAG;IACZ7C,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE;EACZ,CAAC;EAEDuC,uBAAuB,CAACM,OAAO,CAAC,CAACC,aAAa,EAAEvE,GAAG,KAAK;IACtDF,aAAa,CAAC;MAAC,GAAGyE,aAAa;MAAEvE;IAAG,CAAC,CAAC;IAEtCuE,aAAa,CAAC3B,YAAY,CAAC0B,OAAO,CAAE1C,WAAW,IAAK;MAClDZ,UAAU,CAACY,WAAW,CAAC;MAEvB,IAAIA,WAAW,CAACyB,IAAI,KAAKC,+BAAiB,CAACC,OAAO,EAAE;QAClDc,KAAK,CAAC5C,QAAQ,EAAE;QAChB;MACF;MAEA,IAAIG,WAAW,CAACyB,IAAI,KAAKC,+BAAiB,CAACE,KAAK,EAAE;QAChDa,KAAK,CAAC7C,MAAM,EAAE;QACd;MACF;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFD,iBAAiB,CAAC8C,KAAK,CAAC;EAExB,IAAIjC,OAAO,CAACoC,GAAG,EAAE;IACf,OAAO,MAAM,IAAAzC,wBAAe,EAAC;MAC3Ba,YAAY,EAAEe,qBAAqB,CAACK,uBAAuB,CAAC;MAC5DS,iBAAiB,EAAEC,qCAAoB,CAACC,UAAU;MAClDN,KAAK;MACL/B,MAAM;MACNG,eAAe;MACfJ;IACF,CAAC,CAAC;EACJ;EAEA,MAAMuC,sBAAsB,GAAG,MAAM;IACnC,IAAI,OAAOxB,OAAO,CAACyB,KAAK,CAACC,UAAU,KAAK,UAAU,EAAE;MAClD1B,OAAO,CAACyB,KAAK,CAACC,UAAU,CAAC,KAAK,CAAC;IACjC;IACA1B,OAAO,CAACyB,KAAK,CAACE,kBAAkB,CAAC,MAAM,CAAC;EAC1C,CAAC;EAED,MAAMC,UAAU,GAAG,MAAOhF,GAAW,IAAK;IACxC,IAAIA,GAAG,KAAKiF,qBAAI,CAACC,IAAI,IAAIlF,GAAG,KAAK,QAAQ,EAAE;MACzC4E,sBAAsB,EAAE;MAExBxB,OAAO,CAAC+B,IAAI,CAAC,CAAC,CAAC;IACjB;IAEA,IACE,CAACF,qBAAI,CAACG,cAAc,EAAEH,qBAAI,CAACI,UAAU,EAAEJ,qBAAI,CAACK,YAAY,CAAC,CAACC,QAAQ,CAACvF,GAAG,CAAC,EACvE;MACA4E,sBAAsB,EAAE;MAExB,IAAI;QACF,MAAMH,iBAAiB,GAAG;UACxB,CAACQ,qBAAI,CAACG,cAAc,GAAGV,qCAAoB,CAACC,UAAU;UACtD,CAACM,qBAAI,CAACI,UAAU,GAAGX,qCAAoB,CAACc,MAAM;UAC9C,CAACP,qBAAI,CAACK,YAAY,GAAGZ,qCAAoB,CAACe;QAC5C,CAAC;QAED,MAAM,IAAA1D,wBAAe,EAAC;UACpBa,YAAY,EAAEe,qBAAqB,CAACK,uBAAuB,CAAC;UAC5DS,iBAAiB,EAAEA,iBAAiB,CAACzE,GAAG,CAAC;UACzCqE,KAAK;UACL/B,MAAM;UACNG,eAAe;UACfJ;QACF,CAAC,CAAC;QAEFe,OAAO,CAAC+B,IAAI,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,OAAOO,GAAG,EAAE;QACZzF,kBAAM,CAACC,GAAG,CAAEwF,GAAG,CAASC,MAAM,IAAKD,GAAG,CAASE,MAAM,CAAC;QACtD,MAAM,KAAIC,oBAAQ,EAAC,gCAAgC,EAAEH,GAAG,CAAU;MACpE;IACF;EACF,CAAC;EAED,IAAIrB,KAAK,CAAC7C,MAAM,IAAI6C,KAAK,CAAC5C,QAAQ,EAAE;IAClC,IAAAqE,wBAAe,EAAC;MAACd;IAAU,CAAC,CAAC;EAC/B;AACF,CAAqC;AAAC,eAEvB;EACbe,IAAI,EAAE7D,aAAa;EACnB8D,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAE,QAAQ;EACd9E,WAAW,EACT,sEAAsE;EACxEiB,OAAO,EAAE,CACP;IACE6D,IAAI,EAAE,OAAO;IACb9E,WAAW,EAAE;EACf,CAAC,EACD;IACE8E,IAAI,EAAE,eAAe;IACrB9E,WAAW,EACT;EACJ,CAAC;AAEL,CAAC;AAAA"}