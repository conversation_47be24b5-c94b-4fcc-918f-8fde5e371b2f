{"version": 3, "names": ["runElevated", "command", "script", "elevatedPath", "join", "tmpdir", "Math", "random", "writeFileSync", "execa", "groupArgs", "args", "isStringArgument", "newArgs", "reduce", "acum", "current", "last", "length", "endsWith", "startsWith", "push", "replace", "executeShellCommand", "elevated", "split", "program", "shift", "shell"], "sources": ["../../../src/tools/windows/executeWinCommand.ts"], "sourcesContent": ["import {writeFileSync} from 'fs';\nimport {tmpdir} from 'os';\nimport {join} from 'path';\n\nimport execa from 'execa';\n\n/** Runs a command requestion permission to run elevated. */\nconst runElevated = (command: string) => {\n  // TODO: escape double quotes in args\n  // https://www.winhelponline.com/blog/vbscripts-and-uac-elevation/\n  /**\n   * Need to use a couple of intermediary files to make this work as\n   * `ShellExecute` only accepts a command so\n   */\n\n  // prettier-ignore\n  const script =\n`If WScript.Arguments.length = 0 Then\n  Set objShell = CreateObject(\"Shell.Application\")\n  'Pass a bogus argument, say [ uac]\n  objShell.ShellExecute \"wscript.exe\", Chr(34) & _\n    WScript.ScriptFullName & Chr(34) & \" uac\", \"\", \"runas\", 1\nElse\n  Dim oShell\n  Set oShell = WScript.CreateObject (\"WSCript.shell\")\n  oShell.run \"${command}\"\n  Set oShell = Nothing\nEnd If`;\n\n  const elevatedPath = join(tmpdir(), `elevated-${Math.random()}.vbs`);\n\n  writeFileSync(elevatedPath, script, 'utf-8');\n\n  return execa(elevatedPath);\n};\n\n/**\n * Groups all string arguments into a single one. E.g.:\n * ```js\n * ['-m', '\"Upgrade:', 'to', 'latest', 'version\"'] --> ['-m', '\"Upgrade: to latest version\"']`\n * ```\n * @param args The arguments\n * © webhint project\n * (https://github.com/webhintio/hint/blob/30b8ba74f122d8b66fc5596d788dd1c7738f2d83/release/lib/utils.ts#L82)\n * License: Apache-2\n */\nconst groupArgs = (args: string[]) => {\n  let isStringArgument = false;\n  const newArgs = args.reduce((acum: string[], current) => {\n    if (isStringArgument) {\n      const last = acum[acum.length - 1];\n\n      acum[acum.length - 1] = `${last} ${current}`;\n\n      if (current.endsWith('\"')) {\n        isStringArgument = false;\n      }\n\n      return acum;\n    }\n\n    if (current.startsWith('\"')) {\n      /**\n       * Argument is split. I.e.: `['\"part1', 'part2\"'];`\n       */\n      if (!current.endsWith('\"')) {\n        isStringArgument = true;\n\n        acum.push(current);\n\n        return acum;\n      }\n\n      /**\n       * Argument is surrounded by \"\" that need to be removed.\n       * We just remove all the quotes because we don't escape any in our commands\n       */\n      acum.push(current.replace(/\"/g, ''));\n\n      return acum;\n    }\n\n    acum.push(current);\n\n    return acum;\n  }, []);\n\n  return newArgs;\n};\n\n/**\n * Executes the given `command` on a shell taking care of slicing the parameters\n * if needed.\n */\nconst executeShellCommand = (command: string, elevated = false) => {\n  const args = groupArgs(command.split(' '));\n  const program = args.shift()!;\n\n  if (elevated) {\n    return runElevated(command);\n  }\n\n  return execa(program, args, {shell: true});\n};\n\nexport {executeShellCommand as executeCommand};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0B;AAE1B;AACA,MAAMA,WAAW,GAAIC,OAAe,IAAK;EACvC;EACA;EACA;AACF;AACA;AACA;;EAEE;EACA,MAAMC,MAAM,GACb;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBD,OAAQ;AACxB;AACA,OAAO;EAEL,MAAME,YAAY,GAAG,IAAAC,YAAI,EAAC,IAAAC,YAAM,GAAE,EAAG,YAAWC,IAAI,CAACC,MAAM,EAAG,MAAK,CAAC;EAEpE,IAAAC,mBAAa,EAACL,YAAY,EAAED,MAAM,EAAE,OAAO,CAAC;EAE5C,OAAO,IAAAO,gBAAK,EAACN,YAAY,CAAC;AAC5B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMO,SAAS,GAAIC,IAAc,IAAK;EACpC,IAAIC,gBAAgB,GAAG,KAAK;EAC5B,MAAMC,OAAO,GAAGF,IAAI,CAACG,MAAM,CAAC,CAACC,IAAc,EAAEC,OAAO,KAAK;IACvD,IAAIJ,gBAAgB,EAAE;MACpB,MAAMK,IAAI,GAAGF,IAAI,CAACA,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC;MAElCH,IAAI,CAACA,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC,GAAI,GAAED,IAAK,IAAGD,OAAQ,EAAC;MAE5C,IAAIA,OAAO,CAACG,QAAQ,CAAC,GAAG,CAAC,EAAE;QACzBP,gBAAgB,GAAG,KAAK;MAC1B;MAEA,OAAOG,IAAI;IACb;IAEA,IAAIC,OAAO,CAACI,UAAU,CAAC,GAAG,CAAC,EAAE;MAC3B;AACN;AACA;MACM,IAAI,CAACJ,OAAO,CAACG,QAAQ,CAAC,GAAG,CAAC,EAAE;QAC1BP,gBAAgB,GAAG,IAAI;QAEvBG,IAAI,CAACM,IAAI,CAACL,OAAO,CAAC;QAElB,OAAOD,IAAI;MACb;;MAEA;AACN;AACA;AACA;MACMA,IAAI,CAACM,IAAI,CAACL,OAAO,CAACM,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;MAEpC,OAAOP,IAAI;IACb;IAEAA,IAAI,CAACM,IAAI,CAACL,OAAO,CAAC;IAElB,OAAOD,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,OAAOF,OAAO;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAMU,mBAAmB,GAAG,CAACtB,OAAe,EAAEuB,QAAQ,GAAG,KAAK,KAAK;EACjE,MAAMb,IAAI,GAAGD,SAAS,CAACT,OAAO,CAACwB,KAAK,CAAC,GAAG,CAAC,CAAC;EAC1C,MAAMC,OAAO,GAAGf,IAAI,CAACgB,KAAK,EAAG;EAE7B,IAAIH,QAAQ,EAAE;IACZ,OAAOxB,WAAW,CAACC,OAAO,CAAC;EAC7B;EAEA,OAAO,IAAAQ,gBAAK,EAACiB,OAAO,EAAEf,IAAI,EAAE;IAACiB,KAAK,EAAE;EAAI,CAAC,CAAC;AAC5C,CAAC;AAAC"}