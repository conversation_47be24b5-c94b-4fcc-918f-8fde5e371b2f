{"version": 3, "names": ["AUTOMATIC_FIX_LEVELS", "healthchecks", "automaticFixLevel", "stats", "environmentInfo", "config", "process", "stdout", "isTTY", "moveCursor", "clearScreenDown", "totalIssuesBasedOnFixLevel", "ALL_ISSUES", "errors", "warnings", "ERRORS", "WARNINGS", "issuesCount", "logger", "log", "chalk", "bold", "toString", "category", "healthchecksToRun", "filter", "healthcheck", "needsToBeFixed", "type", "HEALTHCHECK_TYPES", "ERROR", "WARNING", "length", "dim", "label", "healthcheckToRun", "spinner", "<PERSON><PERSON><PERSON><PERSON>", "prefixText", "text", "start", "runAutomaticFix", "loader", "logManualInstallation", "error", "stderr"], "sources": ["../../src/tools/runAutomaticFix.ts"], "sourcesContent": ["import {getLoader, logger} from '@react-native-community/cli-tools';\nimport type {Config} from '@react-native-community/cli-types';\nimport chalk from 'chalk';\nimport {EnvironmentInfo, HealthCheckCategoryResult, Loader} from '../types';\nimport {HEALTHCHECK_TYPES} from './healthchecks';\nimport {logManualInstallation} from './healthchecks/common';\n\nexport enum AUTOMATIC_FIX_LEVELS {\n  ALL_ISSUES = 'ALL_ISSUES',\n  ERRORS = 'ERRORS',\n  WARNINGS = 'WARNINGS',\n}\n\ninterface RunAutomaticFixArgs {\n  healthchecks: HealthCheckCategoryResult[];\n  automaticFixLevel: AUTOMATIC_FIX_LEVELS;\n  stats: {\n    errors: number;\n    warnings: number;\n  };\n  loader: Loader;\n  environmentInfo: EnvironmentInfo;\n  config?: Config;\n}\n\nexport default async function ({\n  healthchecks,\n  automaticFixLevel,\n  stats,\n  environmentInfo,\n  config,\n}: RunAutomaticFixArgs) {\n  // Remove the fix options from screen\n  if (process.stdout.isTTY) {\n    process.stdout.moveCursor(0, -6);\n    process.stdout.clearScreenDown();\n  }\n\n  const totalIssuesBasedOnFixLevel: {[x in AUTOMATIC_FIX_LEVELS]: number} = {\n    [AUTOMATIC_FIX_LEVELS.ALL_ISSUES]: stats.errors + stats.warnings,\n    [AUTOMATIC_FIX_LEVELS.ERRORS]: stats.errors,\n    [AUTOMATIC_FIX_LEVELS.WARNINGS]: stats.warnings,\n  };\n  const issuesCount = totalIssuesBasedOnFixLevel[automaticFixLevel];\n\n  logger.log(\n    `\\nAttempting to fix ${chalk.bold(issuesCount.toString())} issue${\n      issuesCount > 1 ? 's' : ''\n    }...`,\n  );\n\n  for (const category of healthchecks) {\n    const healthchecksToRun = category.healthchecks.filter((healthcheck) => {\n      if (automaticFixLevel === AUTOMATIC_FIX_LEVELS.ALL_ISSUES) {\n        return healthcheck.needsToBeFixed;\n      }\n\n      if (automaticFixLevel === AUTOMATIC_FIX_LEVELS.ERRORS) {\n        return (\n          healthcheck.needsToBeFixed &&\n          healthcheck.type === HEALTHCHECK_TYPES.ERROR\n        );\n      }\n\n      if (automaticFixLevel === AUTOMATIC_FIX_LEVELS.WARNINGS) {\n        return (\n          healthcheck.needsToBeFixed &&\n          healthcheck.type === HEALTHCHECK_TYPES.WARNING\n        );\n      }\n\n      return;\n    });\n\n    if (!healthchecksToRun.length) {\n      continue;\n    }\n\n    logger.log(`\\n${chalk.dim(category.label)}`);\n\n    for (const healthcheckToRun of healthchecksToRun) {\n      const spinner = getLoader({\n        prefixText: '',\n        text: healthcheckToRun.label,\n      }).start();\n\n      try {\n        await healthcheckToRun.runAutomaticFix({\n          loader: spinner,\n          logManualInstallation,\n          environmentInfo,\n          config,\n        });\n      } catch (error) {\n        logger.error((error as any).stderr || (error as any).stdout);\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AACA;AAA4D;AAAA,IAEhDA,oBAAoB;AAAA;AAAA,WAApBA,oBAAoB;EAApBA,oBAAoB;EAApBA,oBAAoB;EAApBA,oBAAoB;AAAA,GAApBA,oBAAoB,oCAApBA,oBAAoB;AAkBjB,wBAAgB;EAC7BC,YAAY;EACZC,iBAAiB;EACjBC,KAAK;EACLC,eAAe;EACfC;AACmB,CAAC,EAAE;EACtB;EACA,IAAIC,OAAO,CAACC,MAAM,CAACC,KAAK,EAAE;IACxBF,OAAO,CAACC,MAAM,CAACE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChCH,OAAO,CAACC,MAAM,CAACG,eAAe,EAAE;EAClC;EAEA,MAAMC,0BAAiE,GAAG;IACxE,CAACX,oBAAoB,CAACY,UAAU,GAAGT,KAAK,CAACU,MAAM,GAAGV,KAAK,CAACW,QAAQ;IAChE,CAACd,oBAAoB,CAACe,MAAM,GAAGZ,KAAK,CAACU,MAAM;IAC3C,CAACb,oBAAoB,CAACgB,QAAQ,GAAGb,KAAK,CAACW;EACzC,CAAC;EACD,MAAMG,WAAW,GAAGN,0BAA0B,CAACT,iBAAiB,CAAC;EAEjEgB,kBAAM,CAACC,GAAG,CACP,uBAAsBC,gBAAK,CAACC,IAAI,CAACJ,WAAW,CAACK,QAAQ,EAAE,CAAE,SACxDL,WAAW,GAAG,CAAC,GAAG,GAAG,GAAG,EACzB,KAAI,CACN;EAED,KAAK,MAAMM,QAAQ,IAAItB,YAAY,EAAE;IACnC,MAAMuB,iBAAiB,GAAGD,QAAQ,CAACtB,YAAY,CAACwB,MAAM,CAAEC,WAAW,IAAK;MACtE,IAAIxB,iBAAiB,KAAKF,oBAAoB,CAACY,UAAU,EAAE;QACzD,OAAOc,WAAW,CAACC,cAAc;MACnC;MAEA,IAAIzB,iBAAiB,KAAKF,oBAAoB,CAACe,MAAM,EAAE;QACrD,OACEW,WAAW,CAACC,cAAc,IAC1BD,WAAW,CAACE,IAAI,KAAKC,+BAAiB,CAACC,KAAK;MAEhD;MAEA,IAAI5B,iBAAiB,KAAKF,oBAAoB,CAACgB,QAAQ,EAAE;QACvD,OACEU,WAAW,CAACC,cAAc,IAC1BD,WAAW,CAACE,IAAI,KAAKC,+BAAiB,CAACE,OAAO;MAElD;MAEA;IACF,CAAC,CAAC;IAEF,IAAI,CAACP,iBAAiB,CAACQ,MAAM,EAAE;MAC7B;IACF;IAEAd,kBAAM,CAACC,GAAG,CAAE,KAAIC,gBAAK,CAACa,GAAG,CAACV,QAAQ,CAACW,KAAK,CAAE,EAAC,CAAC;IAE5C,KAAK,MAAMC,gBAAgB,IAAIX,iBAAiB,EAAE;MAChD,MAAMY,OAAO,GAAG,IAAAC,qBAAS,EAAC;QACxBC,UAAU,EAAE,EAAE;QACdC,IAAI,EAAEJ,gBAAgB,CAACD;MACzB,CAAC,CAAC,CAACM,KAAK,EAAE;MAEV,IAAI;QACF,MAAML,gBAAgB,CAACM,eAAe,CAAC;UACrCC,MAAM,EAAEN,OAAO;UACfO,qBAAqB,EAArBA,6BAAqB;UACrBvC,eAAe;UACfC;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOuC,KAAK,EAAE;QACd1B,kBAAM,CAAC0B,KAAK,CAAEA,KAAK,CAASC,MAAM,IAAKD,KAAK,CAASrC,MAAM,CAAC;MAC9D;IACF;EACF;AACF"}