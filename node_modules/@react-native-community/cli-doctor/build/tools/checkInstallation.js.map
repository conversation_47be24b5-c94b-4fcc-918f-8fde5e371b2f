{"version": 3, "names": ["PACKAGE_MANAGERS", "isSoftwareNotInstalled", "command", "commandExists", "_ignored", "doesSoftwareNeedToBeFixed", "version", "versionRange", "<PERSON><PERSON><PERSON><PERSON>", "coercedVersion", "semver", "coerce", "loose", "satisfies"], "sources": ["../../src/tools/checkInstallation.ts"], "sourcesContent": ["import semver from 'semver';\nimport commandExists from 'command-exists';\n\nexport enum PACKAGE_MANAGERS {\n  YARN = 'YARN',\n  NPM = 'NPM',\n  BUN = 'BUN',\n}\n\nconst isSoftwareNotInstalled = async (command: string): Promise<boolean> => {\n  try {\n    await commandExists(command);\n\n    return false;\n  } catch (_ignored) {\n    return true;\n  }\n};\n\nconst doesSoftwareNeedToBeFixed = ({\n  version,\n  versionRange,\n  looseRange = false,\n}: {\n  version: string;\n  versionRange: string;\n  looseRange?: boolean;\n}): boolean => {\n  const coercedVersion = semver.coerce(version, {loose: looseRange});\n\n  return (\n    version === 'Not Found' ||\n    coercedVersion === null ||\n    !semver.satisfies(coercedVersion, versionRange)\n  );\n};\n\nexport {isSoftwareNotInstalled, doesSoftwareNeedToBeFixed};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA2C;AAAA,IAE/BA,gBAAgB;AAAA;AAAA,WAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;AAAA,GAAhBA,gBAAgB,gCAAhBA,gBAAgB;AAM5B,MAAMC,sBAAsB,GAAG,MAAOC,OAAe,IAAuB;EAC1E,IAAI;IACF,MAAM,IAAAC,wBAAa,EAACD,OAAO,CAAC;IAE5B,OAAO,KAAK;EACd,CAAC,CAAC,OAAOE,QAAQ,EAAE;IACjB,OAAO,IAAI;EACb;AACF,CAAC;AAAC;AAEF,MAAMC,yBAAyB,GAAG,CAAC;EACjCC,OAAO;EACPC,YAAY;EACZC,UAAU,GAAG;AAKf,CAAC,KAAc;EACb,MAAMC,cAAc,GAAGC,iBAAM,CAACC,MAAM,CAACL,OAAO,EAAE;IAACM,KAAK,EAAEJ;EAAU,CAAC,CAAC;EAElE,OACEF,OAAO,KAAK,WAAW,IACvBG,cAAc,KAAK,IAAI,IACvB,CAACC,iBAAM,CAACG,SAAS,CAACJ,cAAc,EAAEF,YAAY,CAAC;AAEnD,CAAC;AAAC"}