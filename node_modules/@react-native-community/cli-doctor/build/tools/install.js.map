{"version": 3, "names": ["install", "pkg", "label", "url", "loader", "process", "platform", "brewInstall", "Error", "_error", "fail", "logManualInstallation", "healthcheck"], "sources": ["../../src/tools/install.ts"], "sourcesContent": ["import {Loader} from '../types';\nimport {brewInstall} from './brewInstall';\nimport {logManualInstallation} from './healthchecks/common';\n\ntype InstallArgs = {\n  pkg: string;\n  label: string;\n  url: string;\n  loader: Loader;\n};\n\nasync function install({pkg, label, url, loader}: InstallArgs) {\n  try {\n    switch (process.platform) {\n      case 'darwin':\n        await brewInstall({pkg, label, loader});\n        break;\n      default:\n        throw new Error('Not implemented yet');\n    }\n  } catch (_error) {\n    loader.fail();\n\n    logManualInstallation({\n      healthcheck: label,\n      url,\n    });\n  }\n}\n\nexport {install};\n"], "mappings": ";;;;;;AACA;AACA;AASA,eAAeA,OAAO,CAAC;EAACC,GAAG;EAAEC,KAAK;EAAEC,GAAG;EAAEC;AAAmB,CAAC,EAAE;EAC7D,IAAI;IACF,QAAQC,OAAO,CAACC,QAAQ;MACtB,KAAK,QAAQ;QACX,MAAM,IAAAC,wBAAW,EAAC;UAACN,GAAG;UAAEC,KAAK;UAAEE;QAAM,CAAC,CAAC;QACvC;MACF;QACE,MAAM,IAAII,KAAK,CAAC,qBAAqB,CAAC;IAAC;EAE7C,CAAC,CAAC,OAAOC,MAAM,EAAE;IACfL,MAAM,CAACM,IAAI,EAAE;IAEb,IAAAC,6BAAqB,EAAC;MACpBC,WAAW,EAAEV,KAAK;MAClBC;IACF,CAAC,CAAC;EACJ;AACF"}