{"version": 3, "names": ["label", "description", "getDiagnostics", "Languages", "needsToBeFixed", "doesSoftwareNeedToBeFixed", "version", "Java", "versionRange", "versionRanges", "JAVA", "win32AutomaticFix", "loader", "installerUrl", "installPath", "process", "env", "LOCALAPPDATA", "downloadAndUnzip", "downloadUrl", "component", "text", "jdkPath", "join", "setEnvironment", "updateEnvironment", "succeed", "e", "fail", "runAutomaticFix", "logManualInstallation", "healthcheck", "url", "link", "docs", "hash", "guide"], "sources": ["../../../src/tools/healthchecks/jdk.ts"], "sourcesContent": ["import {join} from 'path';\n\nimport {link} from '@react-native-community/cli-tools';\n\nimport versionRanges from '../versionRanges';\nimport {doesSoftwareNeedToBeFixed} from '../checkInstallation';\nimport {HealthCheckInterface} from '../../types';\nimport {downloadAndUnzip} from '../downloadAndUnzip';\nimport {\n  setEnvironment,\n  updateEnvironment,\n} from '../windows/environmentVariables';\n\nexport default {\n  label: 'JDK',\n  description: 'Required to compile Java code',\n  getDiagnostics: async ({Languages}) => ({\n    needsToBeFixed: doesSoftwareNeedToBeFixed({\n      version:\n        typeof Languages.Java === 'string'\n          ? Languages.Java\n          : Languages.Java.version,\n      versionRange: versionRanges.JAVA,\n    }),\n\n    version:\n      typeof Languages.Java === 'string'\n        ? Languages.Java\n        : Languages.Java.version,\n    versionRange: versionRanges.JAVA,\n  }),\n  win32AutomaticFix: async ({loader}) => {\n    try {\n      // Installing JDK 11 because later versions seem to cause issues with gradle at the moment\n      const installerUrl =\n        'https://download.java.net/java/GA/jdk11/9/GPL/openjdk-11.0.2_windows-x64_bin.zip';\n      const installPath = process.env.LOCALAPPDATA || ''; // The zip is in a folder `jdk-11.02` so it can be unzipped directly there\n\n      await downloadAndUnzip({\n        loader,\n        downloadUrl: installerUrl,\n        component: 'JDK',\n        installPath,\n      });\n\n      loader.text = 'Updating environment variables';\n\n      const jdkPath = join(installPath, 'jdk-11.0.2');\n\n      await setEnvironment('JAVA_HOME', jdkPath);\n      await updateEnvironment('PATH', join(jdkPath, 'bin'));\n\n      loader.succeed(\n        'JDK installed successfully. Please restart your shell to see the changes',\n      );\n    } catch (e) {\n      loader.fail(e as any);\n    }\n  },\n  runAutomaticFix: async ({logManualInstallation, loader}) => {\n    loader.fail();\n    logManualInstallation({\n      healthcheck: 'JDK',\n      url: link.docs('environment-setup', 'android', {\n        hash: 'jdk-studio',\n        guide: 'native',\n      }),\n    });\n  },\n} as HealthCheckInterface;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AACA;AAEA;AACA;AAGyC;AAAA,eAE1B;EACbA,KAAK,EAAE,KAAK;EACZC,WAAW,EAAE,+BAA+B;EAC5CC,cAAc,EAAE,OAAO;IAACC;EAAS,CAAC,MAAM;IACtCC,cAAc,EAAE,IAAAC,4CAAyB,EAAC;MACxCC,OAAO,EACL,OAAOH,SAAS,CAACI,IAAI,KAAK,QAAQ,GAC9BJ,SAAS,CAACI,IAAI,GACdJ,SAAS,CAACI,IAAI,CAACD,OAAO;MAC5BE,YAAY,EAAEC,sBAAa,CAACC;IAC9B,CAAC,CAAC;IAEFJ,OAAO,EACL,OAAOH,SAAS,CAACI,IAAI,KAAK,QAAQ,GAC9BJ,SAAS,CAACI,IAAI,GACdJ,SAAS,CAACI,IAAI,CAACD,OAAO;IAC5BE,YAAY,EAAEC,sBAAa,CAACC;EAC9B,CAAC,CAAC;EACFC,iBAAiB,EAAE,OAAO;IAACC;EAAM,CAAC,KAAK;IACrC,IAAI;MACF;MACA,MAAMC,YAAY,GAChB,kFAAkF;MACpF,MAAMC,WAAW,GAAGC,OAAO,CAACC,GAAG,CAACC,YAAY,IAAI,EAAE,CAAC,CAAC;;MAEpD,MAAM,IAAAC,kCAAgB,EAAC;QACrBN,MAAM;QACNO,WAAW,EAAEN,YAAY;QACzBO,SAAS,EAAE,KAAK;QAChBN;MACF,CAAC,CAAC;MAEFF,MAAM,CAACS,IAAI,GAAG,gCAAgC;MAE9C,MAAMC,OAAO,GAAG,IAAAC,YAAI,EAACT,WAAW,EAAE,YAAY,CAAC;MAE/C,MAAM,IAAAU,oCAAc,EAAC,WAAW,EAAEF,OAAO,CAAC;MAC1C,MAAM,IAAAG,uCAAiB,EAAC,MAAM,EAAE,IAAAF,YAAI,EAACD,OAAO,EAAE,KAAK,CAAC,CAAC;MAErDV,MAAM,CAACc,OAAO,CACZ,0EAA0E,CAC3E;IACH,CAAC,CAAC,OAAOC,CAAC,EAAE;MACVf,MAAM,CAACgB,IAAI,CAACD,CAAC,CAAQ;IACvB;EACF,CAAC;EACDE,eAAe,EAAE,OAAO;IAACC,qBAAqB;IAAElB;EAAM,CAAC,KAAK;IAC1DA,MAAM,CAACgB,IAAI,EAAE;IACbE,qBAAqB,CAAC;MACpBC,WAAW,EAAE,KAAK;MAClBC,GAAG,EAAEC,gBAAI,CAACC,IAAI,CAAC,mBAAmB,EAAE,SAAS,EAAE;QAC7CC,IAAI,EAAE,YAAY;QAClBC,KAAK,EAAE;MACT,CAAC;IACH,CAAC,CAAC;EACJ;AACF,CAAC;AAAA"}