{"version": 3, "names": ["output", "OK", "NO_GEMFILE", "NO_RUBY", "BUNDLE_INVALID_RUBY", "UNKNOWN", "checkRubyGemfileRequirement", "projectRoot", "evaluateGemfile", "inline", "execa", "cwd", "e", "code", "stderr", "message", "label", "isRequired", "description", "getDiagnostics", "Languages", "findProjectRoot", "logger", "debug", "fallback<PERSON><PERSON><PERSON>", "needsToBeFixed", "doesSoftwareNeedToBeFixed", "version", "<PERSON>", "versionRange", "versionRanges", "RUBY", "versionOrError", "chalk", "bold", "dim", "warn", "runAutomaticFix", "loader", "logManualInstallation", "fail", "healthcheck", "url", "link", "docs", "hash", "guide"], "sources": ["../../../src/tools/healthchecks/ruby.ts"], "sourcesContent": ["import execa from 'execa';\nimport chalk from 'chalk';\n\nimport {logger, findProjectRoot, link} from '@react-native-community/cli-tools';\n\nimport versionRanges from '../versionRanges';\nimport {doesSoftwareNeedToBeFixed} from '../checkInstallation';\nimport {HealthCheckInterface} from '../../types';\nimport {inline} from './common';\n\n// Exposed for testing only\nexport const output = {\n  OK: 'Ok',\n  NO_GEMFILE: 'No Gemfile',\n  NO_RUBY: 'No Ruby',\n  BUNDLE_INVALID_RUBY: 'Bundle invalid Ruby',\n  UNKNOWN: 'Unknown',\n} as const;\n\n// The Change:\n// -----------\n//\n// React Native 0.72 primarily defines the compatible version of Ruby in the\n// project's Gemfile [1]. It does this because it allows for ranges instead of\n// pinning to a version of Ruby.\n//\n// In previous versions the .ruby-version file defined the compatible version,\n// and it was derived in the Gemfile [2]:\n//\n// > ruby File.read(File.join(__dir__, '.ruby-version')).strip\n//\n// Why all of the changes with <PERSON>?\n// ---------------------------------\n//\n// React Native has had to weigh up a couple of concerns:\n//\n// - Cocoapods: we don't control the minimum supported version, although that\n//   was defined almost a decade ago [3]. Practically system Ruby on macOS works\n//   for our users.\n//\n// - Apple may drop support for scripting language runtimes in future version of\n//   macOS [4]. Ruby 2.7 is effectively EOL, which means many supporting tools and\n//   developer environments _may_ not support it going forward, and 3.0 is becoming\n//   the default in, for example, places like our CI. Some users may be unable to\n//   install Ruby 2.7 on their devices as a matter of policy.\n//\n// - Our Codegen is extensively built in Ruby 2.7.\n//\n// - A common pain-point for users (old and new) setting up their environment is\n//   configuring a Ruby version manager or managing multiple Ruby versions on their\n//   device. This occurs so frequently that we've removed the step from our docs [6]\n//\n// After users suggested bumping Ruby to 3.1.3 [5], a discussion concluded that\n// allowing a range of version of Ruby (>= 2.6.10) was the best way forward. This\n// balanced the need to make the platform easier to start with, but unblocked more\n// sophisticated users.\n//\n// [1] https://github.com/facebook/react-native/pull/36281\n// [2] https://github.com/facebook/react-native/blob/v0.71.3/Gemfile#L4\n// [3] https://github.com/CocoaPods/guides.cocoapods.org/commit/30881800ac2bd431d9c5d7ee74404b13e7f43888\n// [4] https://developer.apple.com/documentation/macos-release-notes/macos-catalina-10_15-release-notes#Scripting-Language-Runtimes\n// [5] https://github.com/facebook/react-native/pull/36074\n// [6] https://github.com/facebook/react-native-website/commit/8db97602347a8623f21e3e516245d04bdf6f1a29\n\nasync function checkRubyGemfileRequirement(\n  projectRoot: string,\n): Promise<[string, string?]> {\n  const evaluateGemfile = inline`\n  require \"Bundler\"\n  gemfile = Bundler::Definition.build(\"Gemfile\", nil, {})\n  version = gemfile.ruby_version.engine_versions.join(\", \")\n  begin\n    gemfile.validate_runtime!\n  rescue Bundler::GemfileNotFound\n    puts \"${output.NO_GEMFILE}\"\n    exit 1\n  rescue Bundler::RubyVersionMismatch\n    puts \"${output.BUNDLE_INVALID_RUBY}\"\n    STDERR.puts version\n    exit 2\n  rescue => e\n    STDERR e.message\n    exit 3\n  else\n    puts \"${output.OK}\"\n    STDERR.puts version\n  end`;\n\n  try {\n    await execa('ruby', ['-e', evaluateGemfile], {\n      cwd: projectRoot,\n    });\n    return [output.OK];\n  } catch (e) {\n    switch ((e as any).code) {\n      case 'ENOENT':\n        return [output.NO_RUBY];\n      case 1:\n        return [output.NO_GEMFILE];\n      case 2:\n        return [output.BUNDLE_INVALID_RUBY, (e as any).stderr];\n      default:\n        return [output.UNKNOWN, (e as any).message];\n    }\n  }\n}\n\nexport default {\n  label: 'Ruby',\n  isRequired: false,\n  description: 'Required for installing iOS dependencies',\n  getDiagnostics: async ({Languages}) => {\n    let projectRoot;\n    try {\n      projectRoot = findProjectRoot();\n    } catch (e) {\n      logger.debug((e as any).message);\n    }\n\n    const fallbackResult = {\n      needsToBeFixed: doesSoftwareNeedToBeFixed({\n        version: Languages.Ruby.version,\n        versionRange: versionRanges.RUBY,\n      }),\n      version: Languages.Ruby.version,\n      versionRange: versionRanges.RUBY,\n      description: '',\n    };\n\n    // No guidance from the project, so we make the best guess\n    if (!projectRoot) {\n      return fallbackResult;\n    }\n\n    // Gemfile\n    let [code, versionOrError] = await checkRubyGemfileRequirement(projectRoot);\n    switch (code) {\n      case output.OK: {\n        return {\n          needsToBeFixed: false,\n          version: Languages.Ruby.version,\n          versionRange: versionOrError,\n        };\n      }\n      case output.BUNDLE_INVALID_RUBY:\n        return {\n          needsToBeFixed: true,\n          version: Languages.Ruby.version,\n          versionRange: versionOrError,\n        };\n      case output.NO_RUBY:\n        return {\n          needsToBeFixed: true,\n          description: 'Cannot find a working copy of Ruby.',\n        };\n      case output.NO_GEMFILE:\n        fallbackResult.description = `Could not find the project ${chalk.bold(\n          'Gemfile',\n        )} in your project folder (${chalk.dim(\n          projectRoot,\n        )}), guessed using my built-in version.`;\n        break;\n      default:\n        if (versionOrError) {\n          logger.warn(versionOrError);\n        }\n        break;\n    }\n\n    return fallbackResult;\n  },\n  runAutomaticFix: async ({loader, logManualInstallation}) => {\n    loader.fail();\n\n    logManualInstallation({\n      healthcheck: 'Ruby',\n      url: link.docs('environment-setup', 'ios', {\n        hash: 'ruby',\n        guide: 'native',\n      }),\n    });\n  },\n} as HealthCheckInterface;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AACA;AAEA;AAAgC;AAEhC;AACO,MAAMA,MAAM,GAAG;EACpBC,EAAE,EAAE,IAAI;EACRC,UAAU,EAAE,YAAY;EACxBC,OAAO,EAAE,SAAS;EAClBC,mBAAmB,EAAE,qBAAqB;EAC1CC,OAAO,EAAE;AACX,CAAU;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA,eAAeC,2BAA2B,CACxCC,WAAmB,EACS;EAC5B,MAAMC,eAAe,GAAG,IAAAC,cAAM,CAAC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,YAAYT,MAAM,CAACE,UAAW;AAC9B;AACA;AACA,YAAYF,MAAM,CAACI,mBAAoB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,YAAYJ,MAAM,CAACC,EAAG;AACtB;AACA,MAAM;EAEJ,IAAI;IACF,MAAM,IAAAS,gBAAK,EAAC,MAAM,EAAE,CAAC,IAAI,EAAEF,eAAe,CAAC,EAAE;MAC3CG,GAAG,EAAEJ;IACP,CAAC,CAAC;IACF,OAAO,CAACP,MAAM,CAACC,EAAE,CAAC;EACpB,CAAC,CAAC,OAAOW,CAAC,EAAE;IACV,QAASA,CAAC,CAASC,IAAI;MACrB,KAAK,QAAQ;QACX,OAAO,CAACb,MAAM,CAACG,OAAO,CAAC;MACzB,KAAK,CAAC;QACJ,OAAO,CAACH,MAAM,CAACE,UAAU,CAAC;MAC5B,KAAK,CAAC;QACJ,OAAO,CAACF,MAAM,CAACI,mBAAmB,EAAGQ,CAAC,CAASE,MAAM,CAAC;MACxD;QACE,OAAO,CAACd,MAAM,CAACK,OAAO,EAAGO,CAAC,CAASG,OAAO,CAAC;IAAC;EAElD;AACF;AAAC,eAEc;EACbC,KAAK,EAAE,MAAM;EACbC,UAAU,EAAE,KAAK;EACjBC,WAAW,EAAE,0CAA0C;EACvDC,cAAc,EAAE,OAAO;IAACC;EAAS,CAAC,KAAK;IACrC,IAAIb,WAAW;IACf,IAAI;MACFA,WAAW,GAAG,IAAAc,2BAAe,GAAE;IACjC,CAAC,CAAC,OAAOT,CAAC,EAAE;MACVU,kBAAM,CAACC,KAAK,CAAEX,CAAC,CAASG,OAAO,CAAC;IAClC;IAEA,MAAMS,cAAc,GAAG;MACrBC,cAAc,EAAE,IAAAC,4CAAyB,EAAC;QACxCC,OAAO,EAAEP,SAAS,CAACQ,IAAI,CAACD,OAAO;QAC/BE,YAAY,EAAEC,sBAAa,CAACC;MAC9B,CAAC,CAAC;MACFJ,OAAO,EAAEP,SAAS,CAACQ,IAAI,CAACD,OAAO;MAC/BE,YAAY,EAAEC,sBAAa,CAACC,IAAI;MAChCb,WAAW,EAAE;IACf,CAAC;;IAED;IACA,IAAI,CAACX,WAAW,EAAE;MAChB,OAAOiB,cAAc;IACvB;;IAEA;IACA,IAAI,CAACX,IAAI,EAAEmB,cAAc,CAAC,GAAG,MAAM1B,2BAA2B,CAACC,WAAW,CAAC;IAC3E,QAAQM,IAAI;MACV,KAAKb,MAAM,CAACC,EAAE;QAAE;UACd,OAAO;YACLwB,cAAc,EAAE,KAAK;YACrBE,OAAO,EAAEP,SAAS,CAACQ,IAAI,CAACD,OAAO;YAC/BE,YAAY,EAAEG;UAChB,CAAC;QACH;MACA,KAAKhC,MAAM,CAACI,mBAAmB;QAC7B,OAAO;UACLqB,cAAc,EAAE,IAAI;UACpBE,OAAO,EAAEP,SAAS,CAACQ,IAAI,CAACD,OAAO;UAC/BE,YAAY,EAAEG;QAChB,CAAC;MACH,KAAKhC,MAAM,CAACG,OAAO;QACjB,OAAO;UACLsB,cAAc,EAAE,IAAI;UACpBP,WAAW,EAAE;QACf,CAAC;MACH,KAAKlB,MAAM,CAACE,UAAU;QACpBsB,cAAc,CAACN,WAAW,GAAI,8BAA6Be,gBAAK,CAACC,IAAI,CACnE,SAAS,CACT,4BAA2BD,gBAAK,CAACE,GAAG,CACpC5B,WAAW,CACX,uCAAsC;QACxC;MACF;QACE,IAAIyB,cAAc,EAAE;UAClBV,kBAAM,CAACc,IAAI,CAACJ,cAAc,CAAC;QAC7B;QACA;IAAM;IAGV,OAAOR,cAAc;EACvB,CAAC;EACDa,eAAe,EAAE,OAAO;IAACC,MAAM;IAAEC;EAAqB,CAAC,KAAK;IAC1DD,MAAM,CAACE,IAAI,EAAE;IAEbD,qBAAqB,CAAC;MACpBE,WAAW,EAAE,MAAM;MACnBC,GAAG,EAAEC,gBAAI,CAACC,IAAI,CAAC,mBAAmB,EAAE,KAAK,EAAE;QACzCC,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE;MACT,CAAC;IACH,CAAC,CAAC;EACJ;AACF,CAAC;AAAA"}