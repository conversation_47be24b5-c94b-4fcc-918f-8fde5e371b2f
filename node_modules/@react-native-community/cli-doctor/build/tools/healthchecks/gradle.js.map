{"version": 3, "names": ["label", "description", "platform", "process", "getDiagnostics", "_", "config", "projectRoot", "findProjectRoot", "filename", "androidFolderPath", "project", "android", "sourceDir", "gradleWrapperFile", "path", "join", "executableMode", "fs", "constants", "X_OK", "access", "needsToBeFixed", "runAutomaticFix", "loader", "root", "PERMISSIONS", "chmod", "error", "logError", "healthcheck", "command"], "sources": ["../../../src/tools/healthchecks/gradle.ts"], "sourcesContent": ["import fs from 'fs/promises';\nimport path from 'path';\n\nimport {HealthCheckInterface} from '../../types';\nimport {findProjectRoot} from '@react-native-community/cli-tools';\nimport {logError} from './common';\n\nconst label = 'Gradlew';\nconst description = 'Build tool required for Android builds';\n\nconst platform = process.platform as 'darwin' | 'win32' | 'linux';\n\nexport default {\n  label,\n  description,\n  getDiagnostics: async (_, config) => {\n    const projectRoot = findProjectRoot();\n    const filename = platform === 'win32' ? 'gradlew.bat' : 'gradlew';\n    const androidFolderPath =\n      config?.project.android?.sourceDir ?? `${projectRoot}/android`;\n\n    const gradleWrapperFile = path.join(androidFolderPath, filename);\n\n    const executableMode = fs.constants.X_OK;\n\n    try {\n      await fs.access(gradleWrapperFile, executableMode);\n      return {needsToBeFixed: false};\n    } catch {\n      return {needsToBeFixed: true};\n    }\n  },\n  runAutomaticFix: async ({loader, config}) => {\n    try {\n      const projectRoot = config?.root ?? findProjectRoot();\n      const filename = platform === 'win32' ? 'gradlew.bat' : 'gradlew';\n      const androidFolderPath =\n        config?.project.android?.sourceDir ?? `${projectRoot}/android`;\n\n      const gradleWrapperFile = path.join(androidFolderPath, filename);\n      const PERMISSIONS = 0o755;\n\n      await fs.chmod(gradleWrapperFile, PERMISSIONS);\n    } catch (error) {\n      logError({\n        healthcheck: label,\n        loader,\n        error: error as Error,\n        command: 'chmod +x gradlew',\n      });\n    }\n  },\n} as HealthCheckInterface;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAGA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAAkC;AAElC,MAAMA,KAAK,GAAG,SAAS;AACvB,MAAMC,WAAW,GAAG,wCAAwC;AAE5D,MAAMC,QAAQ,GAAGC,OAAO,CAACD,QAAwC;AAAC,eAEnD;EACbF,KAAK;EACLC,WAAW;EACXG,cAAc,EAAE,OAAOC,CAAC,EAAEC,MAAM,KAAK;IAAA;IACnC,MAAMC,WAAW,GAAG,IAAAC,2BAAe,GAAE;IACrC,MAAMC,QAAQ,GAAGP,QAAQ,KAAK,OAAO,GAAG,aAAa,GAAG,SAAS;IACjE,MAAMQ,iBAAiB,GACrB,CAAAJ,MAAM,aAANA,MAAM,gDAANA,MAAM,CAAEK,OAAO,CAACC,OAAO,0DAAvB,sBAAyBC,SAAS,KAAK,GAAEN,WAAY,UAAS;IAEhE,MAAMO,iBAAiB,GAAGC,eAAI,CAACC,IAAI,CAACN,iBAAiB,EAAED,QAAQ,CAAC;IAEhE,MAAMQ,cAAc,GAAGC,mBAAE,CAACC,SAAS,CAACC,IAAI;IAExC,IAAI;MACF,MAAMF,mBAAE,CAACG,MAAM,CAACP,iBAAiB,EAAEG,cAAc,CAAC;MAClD,OAAO;QAACK,cAAc,EAAE;MAAK,CAAC;IAChC,CAAC,CAAC,MAAM;MACN,OAAO;QAACA,cAAc,EAAE;MAAI,CAAC;IAC/B;EACF,CAAC;EACDC,eAAe,EAAE,OAAO;IAACC,MAAM;IAAElB;EAAM,CAAC,KAAK;IAC3C,IAAI;MAAA;MACF,MAAMC,WAAW,GAAG,CAAAD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEmB,IAAI,KAAI,IAAAjB,2BAAe,GAAE;MACrD,MAAMC,QAAQ,GAAGP,QAAQ,KAAK,OAAO,GAAG,aAAa,GAAG,SAAS;MACjE,MAAMQ,iBAAiB,GACrB,CAAAJ,MAAM,aAANA,MAAM,iDAANA,MAAM,CAAEK,OAAO,CAACC,OAAO,2DAAvB,uBAAyBC,SAAS,KAAK,GAAEN,WAAY,UAAS;MAEhE,MAAMO,iBAAiB,GAAGC,eAAI,CAACC,IAAI,CAACN,iBAAiB,EAAED,QAAQ,CAAC;MAChE,MAAMiB,WAAW,GAAG,KAAK;MAEzB,MAAMR,mBAAE,CAACS,KAAK,CAACb,iBAAiB,EAAEY,WAAW,CAAC;IAChD,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,IAAAC,gBAAQ,EAAC;QACPC,WAAW,EAAE9B,KAAK;QAClBwB,MAAM;QACNI,KAAK,EAAEA,KAAc;QACrBG,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF;AACF,CAAC;AAAA"}