{"version": 3, "names": ["createLog", "platformName", "_", "ctx", "args", "platformConfig", "project", "readableName", "platformReadableName", "getPlatformInfo", "undefined", "supportedPlatforms", "CLIError", "sdkNames", "allDevices", "listDevices", "simulators", "filter", "type", "length", "logger", "error", "booted", "state", "interactive", "udid", "promptForDeviceToTailLogs", "simulator", "find", "deviceUDID", "tailDeviceLogs", "device", "logDir", "path", "join", "os", "homedir", "info", "name", "log", "spawnSync", "stdio"], "sources": ["../../../src/commands/logCommand/createLog.ts"], "sourcesContent": ["import {CLIError, logger} from '@react-native-community/cli-tools';\nimport {Config, IOSProjectConfig} from '@react-native-community/cli-types';\nimport {spawnSync} from 'child_process';\nimport os from 'os';\nimport path from 'path';\nimport listDevices from '../../tools/listDevices';\nimport {getPlatformInfo} from '../runCommand/getPlatformInfo';\nimport {BuilderCommand, Device} from '../../types';\nimport {supportedPlatforms} from '@react-native-community/cli-config-apple';\nimport {promptForDeviceToTailLogs} from '../../tools/prompts';\n\n/**\n * Starts Apple device syslog tail\n */\n\ntype Args = {\n  interactive: boolean;\n};\n\nconst createLog =\n  ({platformName}: BuilderCommand) =>\n  async (_: Array<string>, ctx: Config, args: Args) => {\n    const platformConfig = ctx.project[platformName] as IOSProjectConfig;\n    const {readableName: platformReadableName} = getPlatformInfo(platformName);\n\n    if (\n      platformConfig === undefined ||\n      supportedPlatforms[platformName] === undefined\n    ) {\n      throw new CLIError(`Unable to find ${platformName} platform config`);\n    }\n\n    const {sdkNames} = getPlatformInfo(platformName);\n    const allDevices = await listDevices(sdkNames);\n    const simulators = allDevices.filter(({type}) => type === 'simulator');\n\n    if (simulators.length === 0) {\n      logger.error('No simulators detected. Install simulators via Xcode.');\n      return;\n    }\n\n    const booted = simulators.filter(({state}) => state === 'Booted');\n\n    if (booted.length === 0) {\n      logger.error(\n        `No booted and available ${platformReadableName} simulators found.`,\n      );\n      return;\n    }\n\n    if (args.interactive && booted.length > 1) {\n      const udid = await promptForDeviceToTailLogs(\n        platformReadableName,\n        booted,\n      );\n\n      const simulator = booted.find(\n        ({udid: deviceUDID}) => deviceUDID === udid,\n      );\n\n      if (!simulator) {\n        throw new CLIError(\n          `Unable to find simulator with udid: ${udid} in booted simulators`,\n        );\n      }\n\n      tailDeviceLogs(simulator);\n    } else {\n      tailDeviceLogs(booted[0]);\n    }\n  };\n\nfunction tailDeviceLogs(device: Device) {\n  const logDir = path.join(\n    os.homedir(),\n    'Library',\n    'Logs',\n    'CoreSimulator',\n    device.udid,\n    'asl',\n  );\n\n  logger.info(`Tailing logs for device ${device.name} (${device.udid})`);\n\n  const log = spawnSync('syslog', ['-w', '-F', 'std', '-d', logDir], {\n    stdio: 'inherit',\n  });\n\n  if (log.error !== null) {\n    throw log.error;\n  }\n}\n\nexport default createLog;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAA8D;AAE9D;AACA;AACA;;AAMA,MAAMA,SAAS,GACb,CAAC;EAACC;AAA4B,CAAC,KAC/B,OAAOC,CAAgB,EAAEC,GAAW,EAAEC,IAAU,KAAK;EACnD,MAAMC,cAAc,GAAGF,GAAG,CAACG,OAAO,CAACL,YAAY,CAAqB;EACpE,MAAM;IAACM,YAAY,EAAEC;EAAoB,CAAC,GAAG,IAAAC,gCAAe,EAACR,YAAY,CAAC;EAE1E,IACEI,cAAc,KAAKK,SAAS,IAC5BC,oCAAkB,CAACV,YAAY,CAAC,KAAKS,SAAS,EAC9C;IACA,MAAM,KAAIE,oBAAQ,EAAE,kBAAiBX,YAAa,kBAAiB,CAAC;EACtE;EAEA,MAAM;IAACY;EAAQ,CAAC,GAAG,IAAAJ,gCAAe,EAACR,YAAY,CAAC;EAChD,MAAMa,UAAU,GAAG,MAAM,IAAAC,oBAAW,EAACF,QAAQ,CAAC;EAC9C,MAAMG,UAAU,GAAGF,UAAU,CAACG,MAAM,CAAC,CAAC;IAACC;EAAI,CAAC,KAAKA,IAAI,KAAK,WAAW,CAAC;EAEtE,IAAIF,UAAU,CAACG,MAAM,KAAK,CAAC,EAAE;IAC3BC,kBAAM,CAACC,KAAK,CAAC,uDAAuD,CAAC;IACrE;EACF;EAEA,MAAMC,MAAM,GAAGN,UAAU,CAACC,MAAM,CAAC,CAAC;IAACM;EAAK,CAAC,KAAKA,KAAK,KAAK,QAAQ,CAAC;EAEjE,IAAID,MAAM,CAACH,MAAM,KAAK,CAAC,EAAE;IACvBC,kBAAM,CAACC,KAAK,CACT,2BAA0Bb,oBAAqB,oBAAmB,CACpE;IACD;EACF;EAEA,IAAIJ,IAAI,CAACoB,WAAW,IAAIF,MAAM,CAACH,MAAM,GAAG,CAAC,EAAE;IACzC,MAAMM,IAAI,GAAG,MAAM,IAAAC,kCAAyB,EAC1ClB,oBAAoB,EACpBc,MAAM,CACP;IAED,MAAMK,SAAS,GAAGL,MAAM,CAACM,IAAI,CAC3B,CAAC;MAACH,IAAI,EAAEI;IAAU,CAAC,KAAKA,UAAU,KAAKJ,IAAI,CAC5C;IAED,IAAI,CAACE,SAAS,EAAE;MACd,MAAM,KAAIf,oBAAQ,EACf,uCAAsCa,IAAK,uBAAsB,CACnE;IACH;IAEAK,cAAc,CAACH,SAAS,CAAC;EAC3B,CAAC,MAAM;IACLG,cAAc,CAACR,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3B;AACF,CAAC;AAEH,SAASQ,cAAc,CAACC,MAAc,EAAE;EACtC,MAAMC,MAAM,GAAGC,eAAI,CAACC,IAAI,CACtBC,aAAE,CAACC,OAAO,EAAE,EACZ,SAAS,EACT,MAAM,EACN,eAAe,EACfL,MAAM,CAACN,IAAI,EACX,KAAK,CACN;EAEDL,kBAAM,CAACiB,IAAI,CAAE,2BAA0BN,MAAM,CAACO,IAAK,KAAIP,MAAM,CAACN,IAAK,GAAE,CAAC;EAEtE,MAAMc,GAAG,GAAG,IAAAC,0BAAS,EAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAER,MAAM,CAAC,EAAE;IACjES,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,IAAIF,GAAG,CAAClB,KAAK,KAAK,IAAI,EAAE;IACtB,MAAMkB,GAAG,CAAClB,KAAK;EACjB;AACF;AAAC,eAEcrB,SAAS;AAAA"}