import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

// Import screens
import StartScreen from './src/screens/StartScreen';
import MatchSettingsScreen from './src/screens/MatchSettingsScreen';
import TeamPlayersScreen from './src/screens/TeamPlayersScreen';
import TossScreen from './src/screens/TossScreen';
import CoinFlipAnimationScreen from './src/screens/CoinFlipAnimationScreen';
import TossDecisionScreen from './src/screens/TossDecisionScreen';
import ScoringScreen from './src/screens/ScoringScreen';

// Create stack navigator
const Stack = createNativeStackNavigator();

function App(): React.JSX.Element {
  return (
    <NavigationContainer>
      <Stack.Navigator initialRouteName="Start">
        <Stack.Screen
          name="Start"
          component={StartScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="MatchSettings"
          component={MatchSettingsScreen}
          options={{ title: 'Match Settings' }}
        />
        <Stack.Screen
          name="TeamPlayers"
          component={TeamPlayersScreen}
          options={{ title: 'Team & Players' }}
        />
        <Stack.Screen
          name="TossScreen"
          component={TossScreen}
          options={{ title: 'Toss' }}
        />
        <Stack.Screen
          name="CoinFlipAnimation"
          component={CoinFlipAnimationScreen}
          options={{ title: 'Coin Toss', headerShown: false }}
        />
        <Stack.Screen
          name="OfflineTossResult"
          component={TossDecisionScreen}
          options={{ title: 'Toss Result' }}
        />
        <Stack.Screen
          name="TossDecision"
          component={TossDecisionScreen}
          options={{ title: 'Toss Decision' }}
        />
        <Stack.Screen
          name="ScoringScreen"
          component={ScoringScreen}
          options={{ title: 'Scoring', headerBackVisible: false }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

export default App;
